/**
 * Panapana Course Automation - Admin Scripts
 * 
 * JavaScript functionality for the WordPress admin interface.
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        PanapanaAdmin.init();
    });

    /**
     * Main admin object
     */
    window.PanapanaAdmin = {
        
        /**
         * Initialize all admin functionality
         */
        init: function() {
            this.initFileUpload();
            this.initProgressBars();
            this.initNotices();
            this.initTooltips();
        },

        /**
         * Initialize file upload functionality
         */
        initFileUpload: function() {
            var $uploadArea = $('.panapana-upload-area');
            var $fileInput = $('#panapana-csv-file');

            if ($uploadArea.length === 0) return;

            // Click to upload
            $uploadArea.on('click', function(e) {
                e.preventDefault();
                console.log('Upload area clicked, triggering file input');
                $fileInput.trigger('click');
            });

            // File input change
            $fileInput.on('change', function() {
                var files = this.files;
                if (files.length > 0) {
                    PanapanaAdmin.handleFileSelection(files[0]);
                }
            });

            // Drag and drop
            $uploadArea.on('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('dragover');
            });

            $uploadArea.on('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('dragover');
            });

            $uploadArea.on('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('dragover');

                var files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    // Set the file in the input field for drag & drop
                    var fileInput = $fileInput[0];
                    var dataTransfer = new DataTransfer();
                    dataTransfer.items.add(files[0]);
                    fileInput.files = dataTransfer.files;

                    PanapanaAdmin.handleFileSelection(files[0]);
                }
            });
        },

        /**
         * Handle file selection
         */
        handleFileSelection: function(file) {
            // Validate file type
            if (!file.name.toLowerCase().endsWith('.csv')) {
                this.showNotice('error', 'Please select a CSV file.');
                return;
            }

            // Validate file size (10MB limit)
            if (file.size > 10 * 1024 * 1024) {
                this.showNotice('error', 'File size must be less than 10MB.');
                return;
            }

            // Store the file for drag & drop uploads
            this.selectedFile = file;

            // Update UI to show selected file
            $('.panapana-upload-text').text('Selected: ' + file.name);
            $('.panapana-upload-hint').text('File ready for upload. Click "Process CSV" to continue.');

            // Enable submit button
            $('#panapana-submit-csv').prop('disabled', false);
        },

        /**
         * Initialize progress bars
         */
        initProgressBars: function() {
            // Handle form submission with progress
            $('#panapana-csv-form').on('submit', function(e) {
                e.preventDefault();
                PanapanaAdmin.processCSVUpload(this);
            });
        },

        /**
         * Process CSV upload with AJAX
         */
        processCSVUpload: function(form) {
            console.log('processCSVUpload called with form:', form);

            // Check if a file is selected
            var fileInput = form.querySelector('#panapana-csv-file');
            if ((!fileInput.files || fileInput.files.length === 0) && !this.selectedFile) {
                this.showNotice('error', 'Please select a CSV file before submitting.');
                return false;
            }

            var $form = $(form);
            var $progressContainer = $('.panapana-progress-container');
            var $progressBar = $('.panapana-progress-fill');
            var $progressText = $('.panapana-progress-text');
            var $submitButton = $('#panapana-submit-csv');

            console.log('Elements found:', {
                form: $form.length,
                progressContainer: $progressContainer.length,
                progressBar: $progressBar.length,
                progressText: $progressText.length,
                submitButton: $submitButton.length
            });

            // Check if panapana_admin is available
            if (typeof panapana_admin === 'undefined') {
                console.error('panapana_admin object not found. Available objects:', Object.keys(window));
                console.error('Falling back to regular form submission.');
                // Submit the form normally
                form.submit();
                return false;
            }

            console.log('panapana_admin object found:', panapana_admin);

            // Show progress bar
            $progressContainer.show();
            $submitButton.prop('disabled', true);
            $progressText.text(panapana_admin.strings.uploading || 'Uploading...');

            // Create FormData
            var formData = new FormData(form);

            // If we have a stored file from drag & drop and the input is empty, add it
            var fileInput = form.querySelector('#panapana-csv-file');
            if (this.selectedFile && (!fileInput.files || fileInput.files.length === 0)) {
                formData.set('csv_file', this.selectedFile);
            }

            formData.append('action', 'panapana_process_csv');
            formData.append('nonce', panapana_admin.nonce);

            // AJAX request
            $.ajax({
                url: panapana_admin.ajax_url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function() {
                    var xhr = new window.XMLHttpRequest();
                    // Upload progress
                    xhr.upload.addEventListener('progress', function(evt) {
                        if (evt.lengthComputable) {
                            var percentComplete = (evt.loaded / evt.total) * 100;
                            $progressBar.css('width', percentComplete + '%');
                        }
                    }, false);
                    return xhr;
                },
                success: function(response) {
                    $progressBar.css('width', '100%');
                    $progressText.text(panapana_admin.strings.success);
                    
                    if (response.success) {
                        PanapanaAdmin.showNotice('success', response.data.message);
                        // Redirect to course management or show results
                        if (response.data.redirect) {
                            setTimeout(function() {
                                window.location.href = response.data.redirect;
                            }, 2000);
                        }
                    } else {
                        PanapanaAdmin.showNotice('error', response.data.message || 'Upload failed');
                    }
                },
                error: function(xhr, status, error) {
                    $progressText.text(panapana_admin.strings.error);
                    PanapanaAdmin.showNotice('error', 'Upload failed: ' + error);
                },
                complete: function() {
                    $submitButton.prop('disabled', false);
                    setTimeout(function() {
                        $progressContainer.hide();
                        $progressBar.css('width', '0%');
                    }, 3000);
                }
            });
        },

        /**
         * Initialize notices
         */
        initNotices: function() {
            // Auto-hide success notices after 5 seconds
            setTimeout(function() {
                $('.panapana-notice.success').fadeOut();
            }, 5000);

            // Dismiss notices on click
            $(document).on('click', '.panapana-notice', function() {
                $(this).fadeOut();
            });
        },

        /**
         * Show notice message
         */
        showNotice: function(type, message) {
            var $notice = $('<div class="panapana-notice ' + type + '">' + message + '</div>');
            $('.panapana-admin-wrap').prepend($notice);
            
            // Auto-hide after 5 seconds for success messages
            if (type === 'success') {
                setTimeout(function() {
                    $notice.fadeOut();
                }, 5000);
            }
        },

        /**
         * Initialize tooltips
         */
        initTooltips: function() {
            // Add tooltips to elements with data-tooltip attribute
            $('[data-tooltip]').each(function() {
                var $this = $(this);
                var tooltip = $this.attr('data-tooltip');
                
                $this.on('mouseenter', function() {
                    var $tooltip = $('<div class="panapana-tooltip">' + tooltip + '</div>');
                    $('body').append($tooltip);
                    
                    var offset = $this.offset();
                    $tooltip.css({
                        position: 'absolute',
                        top: offset.top - $tooltip.outerHeight() - 5,
                        left: offset.left + ($this.outerWidth() / 2) - ($tooltip.outerWidth() / 2),
                        background: '#1d2327',
                        color: '#fff',
                        padding: '5px 10px',
                        borderRadius: '3px',
                        fontSize: '12px',
                        zIndex: 9999
                    });
                });
                
                $this.on('mouseleave', function() {
                    $('.panapana-tooltip').remove();
                });
            });
        },

        /**
         * Refresh system status
         */
        refreshSystemStatus: function() {
            var $button = $('#refresh-system-status');
            var $statusContainer = $('.panapana-system-info');
            
            $button.prop('disabled', true).text('Refreshing...');
            
            $.ajax({
                url: panapana_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'panapana_refresh_system_status',
                    nonce: panapana_admin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $statusContainer.html(response.data.html);
                        PanapanaAdmin.showNotice('success', 'System status refreshed');
                    } else {
                        PanapanaAdmin.showNotice('error', 'Failed to refresh system status');
                    }
                },
                error: function() {
                    PanapanaAdmin.showNotice('error', 'Failed to refresh system status');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Refresh Status');
                }
            });
        },

        /**
         * Export system information
         */
        exportSystemInfo: function() {
            window.location.href = panapana_admin.ajax_url + '?action=panapana_export_system_info&nonce=' + panapana_admin.nonce;
        }
    };

})(jQuery);
