[05-Jul-2025 00:45:28 UTC] PHP Warning:  Attempt to read property "version" on array in /home/<USER>/public_html/wp-content/plugins/tutor-pro/updater/update.php on line 263
[05-Jul-2025 00:45:29 UTC] PHP Warning:  Attempt to read property "version" on array in /home/<USER>/public_html/wp-content/plugins/tutor-pro/updater/update.php on line 263
[06-Jul-2025 21:38:02 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[06-Jul-2025 21:38:02 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[06-Jul-2025 21:38:02 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[06-Jul-2025 21:38:02 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[06-Jul-2025 21:38:02 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[06-Jul-2025 21:38:02 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[06-Jul-2025 21:38:02 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[06-Jul-2025 21:38:02 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[06-Jul-2025 21:38:03 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>all-in-one-wp-migration</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:03 UTC] [Loco.debug] "Unloaded 1 premature text domain (all-in-one-wp-migration)" in src/hooks/LoadHelper.php:65
[06-Jul-2025 21:38:03 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:03 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:03 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>visibility-logic-elementor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:03 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:03 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[06-Jul-2025 21:38:03 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[06-Jul-2025 21:38:03 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[06-Jul-2025 21:38:03 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[06-Jul-2025 21:38:03 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[06-Jul-2025 21:38:03 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[06-Jul-2025 21:38:03 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[06-Jul-2025 21:38:03 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[06-Jul-2025 21:38:04 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>all-in-one-wp-migration</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:04 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>geeks</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:04 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>acf</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:04 UTC] [Loco.debug] "Unloaded 1 premature text domain (all-in-one-wp-migration)" in src/hooks/LoadHelper.php:65
[06-Jul-2025 21:38:04 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:04 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:04 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>visibility-logic-elementor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:04 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:04 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>geeks</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:04 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>acf</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:06 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[06-Jul-2025 21:38:06 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[06-Jul-2025 21:38:06 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[06-Jul-2025 21:38:06 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[06-Jul-2025 21:38:06 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[06-Jul-2025 21:38:06 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[06-Jul-2025 21:38:06 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[06-Jul-2025 21:38:06 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[06-Jul-2025 21:38:06 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>all-in-one-wp-migration</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:06 UTC] [Loco.debug] "Unloaded 1 premature text domain (all-in-one-wp-migration)" in src/hooks/LoadHelper.php:65
[06-Jul-2025 21:38:06 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:06 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:06 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>visibility-logic-elementor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:06 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:07 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>geeks</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:07 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>acf</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:08 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[06-Jul-2025 21:38:08 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[06-Jul-2025 21:38:08 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[06-Jul-2025 21:38:08 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[06-Jul-2025 21:38:08 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[06-Jul-2025 21:38:08 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[06-Jul-2025 21:38:08 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[06-Jul-2025 21:38:08 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[06-Jul-2025 21:38:08 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>all-in-one-wp-migration</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:08 UTC] [Loco.debug] "Unloaded 1 premature text domain (all-in-one-wp-migration)" in src/hooks/LoadHelper.php:65
[06-Jul-2025 21:38:08 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:08 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:08 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>visibility-logic-elementor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:08 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:08 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>geeks</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:08 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>acf</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:09 UTC] Panapana Admin Assets: Hook suffix = course-automation_page_panapana-csv-upload
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[06-Jul-2025 21:38:10 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[06-Jul-2025 21:38:10 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>all-in-one-wp-migration</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:10 UTC] [Loco.debug] "Unloaded 1 premature text domain (all-in-one-wp-migration)" in src/hooks/LoadHelper.php:65
[06-Jul-2025 21:38:10 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>all-in-one-wp-migration</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:10 UTC] [Loco.debug] "Unloaded 1 premature text domain (all-in-one-wp-migration)" in src/hooks/LoadHelper.php:65
[06-Jul-2025 21:38:10 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:10 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:10 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:10 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>visibility-logic-elementor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>visibility-logic-elementor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:11 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>geeks</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:11 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>acf</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:11 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>geeks</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:11 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>acf</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:12 UTC] PHP Deprecated:  Implicit conversion from float 249.3 to int loses precision in G:\xampp\htdocs\plataforma_panapana\wp-content\themes\geeks\inc\geeks-functions.php on line 897
[06-Jul-2025 21:38:12 UTC] PHP Deprecated:  Implicit conversion from float 109.3 to int loses precision in G:\xampp\htdocs\plataforma_panapana\wp-content\themes\geeks\inc\geeks-functions.php on line 898
[06-Jul-2025 21:38:12 UTC] PHP Deprecated:  Implicit conversion from float 250.5 to int loses precision in G:\xampp\htdocs\plataforma_panapana\wp-content\themes\geeks\inc\geeks-functions.php on line 897
[06-Jul-2025 21:38:12 UTC] PHP Deprecated:  Implicit conversion from float 110.5 to int loses precision in G:\xampp\htdocs\plataforma_panapana\wp-content\themes\geeks\inc\geeks-functions.php on line 898
[06-Jul-2025 21:38:12 UTC] PHP Deprecated:  Implicit conversion from float 249.3 to int loses precision in G:\xampp\htdocs\plataforma_panapana\wp-content\themes\geeks\inc\geeks-functions.php on line 897
[06-Jul-2025 21:38:12 UTC] PHP Deprecated:  Implicit conversion from float 109.3 to int loses precision in G:\xampp\htdocs\plataforma_panapana\wp-content\themes\geeks\inc\geeks-functions.php on line 898
[06-Jul-2025 21:38:12 UTC] PHP Deprecated:  Implicit conversion from float 142.7 to int loses precision in G:\xampp\htdocs\plataforma_panapana\wp-content\themes\geeks\inc\geeks-functions.php on line 898
[06-Jul-2025 21:38:12 UTC] PHP Deprecated:  Implicit conversion from float 27.7 to int loses precision in G:\xampp\htdocs\plataforma_panapana\wp-content\themes\geeks\inc\geeks-functions.php on line 899
[06-Jul-2025 21:38:17 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[06-Jul-2025 21:38:17 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[06-Jul-2025 21:38:17 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[06-Jul-2025 21:38:17 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[06-Jul-2025 21:38:17 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[06-Jul-2025 21:38:17 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[06-Jul-2025 21:38:17 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[06-Jul-2025 21:38:17 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[06-Jul-2025 21:38:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>all-in-one-wp-migration</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:17 UTC] [Loco.debug] "Unloaded 1 premature text domain (all-in-one-wp-migration)" in src/hooks/LoadHelper.php:65
[06-Jul-2025 21:38:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>visibility-logic-elementor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:17 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:18 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>geeks</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:18 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>acf</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:19 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[06-Jul-2025 21:38:19 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[06-Jul-2025 21:38:19 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[06-Jul-2025 21:38:19 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[06-Jul-2025 21:38:19 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[06-Jul-2025 21:38:19 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[06-Jul-2025 21:38:19 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[06-Jul-2025 21:38:19 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[06-Jul-2025 21:38:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>all-in-one-wp-migration</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:19 UTC] [Loco.debug] "Unloaded 1 premature text domain (all-in-one-wp-migration)" in src/hooks/LoadHelper.php:65
[06-Jul-2025 21:38:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>visibility-logic-elementor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:19 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:20 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function panapana_handle_csv_upload() in G:\xampp\htdocs\plataforma_panapana\wp-content\themes\geeks-child\tutor\admin\ajax-handlers.php:57
Stack trace:
#0 G:\xampp\htdocs\plataforma_panapana\wp-includes\class-wp-hook.php(324): panapana_ajax_process_csv('')
#1 G:\xampp\htdocs\plataforma_panapana\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#2 G:\xampp\htdocs\plataforma_panapana\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#3 G:\xampp\htdocs\plataforma_panapana\wp-admin\admin-ajax.php(192): do_action('wp_ajax_panapan...')
#4 {main}
  thrown in G:\xampp\htdocs\plataforma_panapana\wp-content\themes\geeks-child\tutor\admin\ajax-handlers.php on line 57
[06-Jul-2025 21:38:20 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>geeks</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:20 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>acf</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:21 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::hasChildren($allow_links = true) should either be compatible with RecursiveDirectoryIterator::hasChildren(bool $allowLinks = false): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 57
[06-Jul-2025 21:38:21 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::rewind() should either be compatible with FilesystemIterator::rewind(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 35
[06-Jul-2025 21:38:21 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Directory_Iterator::next() should either be compatible with DirectoryIterator::next(): void, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\iterator\class-ai1wm-recursive-directory-iterator.php on line 42
[06-Jul-2025 21:38:21 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 47
[06-Jul-2025 21:38:21 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Extension_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-extension-filter.php on line 37
[06-Jul-2025 21:38:21 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::getChildren() should either be compatible with RecursiveFilterIterator::getChildren(): ?RecursiveFilterIterator, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 41
[06-Jul-2025 21:38:21 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Exclude_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-exclude-filter.php on line 37
[06-Jul-2025 21:38:21 UTC] PHP Deprecated:  Return type of Ai1wm_Recursive_Newline_Filter::accept() should either be compatible with FilterIterator::accept(): bool, or the #[\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in G:\xampp\htdocs\plataforma_panapana\wp-content\plugins\All-In-One-WP-Migration-With-Import-master\lib\vendor\servmask\filter\class-ai1wm-recursive-newline-filter.php on line 28
[06-Jul-2025 21:38:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>all-in-one-wp-migration</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:21 UTC] [Loco.debug] "Unloaded 1 premature text domain (all-in-one-wp-migration)" in src/hooks/LoadHelper.php:65
[06-Jul-2025 21:38:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>tutor-pro</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>visibility-logic-elementor</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:21 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>rcp</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:22 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>geeks</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
[06-Jul-2025 21:38:22 UTC] PHP Notice:  A função _load_textdomain_just_in_time foi chamada <strong>incorretamente</strong>. O carregamento da tradução para o domínio <code>acf</code> foi ativado muito cedo. Isso geralmente é um indicador de que algum código no plugin ou tema está sendo executado muito cedo. As traduções devem ser carregadas na ação <code>init</code> ou mais tarde. Leia como <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Depurar o WordPress</a> para mais informações. (Esta mensagem foi adicionada na versão 6.7.0.) in G:\xampp\htdocs\plataforma_panapana\wp-includes\functions.php on line 6121
