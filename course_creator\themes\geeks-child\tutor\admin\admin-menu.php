<?php
/**
 * <PERSON>tor LMS Course Automation - WordPress Admin Menu
 *
 * This file handles the WordPress admin menu registration and page routing
 * for the course automation system.
 *
 * @package TutorLMS_CourseAutomation
 * @subpackage Admin
 * @version 3.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Class to handle WordPress admin menu for course automation.
 */
class Panapana_Admin_Menu {

	/**
	 * Initialize the admin menu system.
	 */
	public static function init() {
		add_action( 'admin_menu', array( __CLASS__, 'register_admin_menu' ) );
		add_action( 'admin_enqueue_scripts', array( __CLASS__, 'enqueue_admin_assets' ) );
	}

	/**
	 * Register the main admin menu and submenus.
	 */
	public static function register_admin_menu() {
		// Only show menu to users with proper capabilities
		if ( ! panapana_user_can_access_automation() ) {
			return;
		}

		// Main menu page - Dashboard
		add_menu_page(
			__( 'Course Automation', 'panapana' ),           // Page title
			__( 'Course Automation', 'panapana' ),           // Menu title
			'export_course_data',                             // Capability
			'panapana-course-automation',                     // Menu slug
			array( __CLASS__, 'render_dashboard_page' ),     // Callback
			'dashicons-welcome-learn-more',                   // Icon
			30                                                // Position
		);

		// Dashboard submenu (same as main page)
		add_submenu_page(
			'panapana-course-automation',                     // Parent slug
			__( 'Dashboard', 'panapana' ),                   // Page title
			__( 'Dashboard', 'panapana' ),                   // Menu title
			'export_course_data',                             // Capability
			'panapana-course-automation',                     // Menu slug (same as parent)
			array( __CLASS__, 'render_dashboard_page' )      // Callback
		);

		// CSV Upload submenu
		add_submenu_page(
			'panapana-course-automation',                     // Parent slug
			__( 'Upload CSV', 'panapana' ),                  // Page title
			__( 'Upload CSV', 'panapana' ),                  // Menu title
			'export_course_data',                             // Capability
			'panapana-csv-upload',                            // Menu slug
			array( __CLASS__, 'render_csv_upload_page' )     // Callback
		);

		// Course Management submenu
		add_submenu_page(
			'panapana-course-automation',                     // Parent slug
			__( 'Manage Courses', 'panapana' ),              // Page title
			__( 'Manage Courses', 'panapana' ),              // Menu title
			'export_course_data',                             // Capability
			'panapana-course-management',                     // Menu slug
			array( __CLASS__, 'render_course_management_page' ) // Callback
		);

		// System Status submenu
		add_submenu_page(
			'panapana-course-automation',                     // Parent slug
			__( 'System Status', 'panapana' ),               // Page title
			__( 'System Status', 'panapana' ),               // Menu title
			'export_course_data',                             // Capability
			'panapana-system-status',                         // Menu slug
			array( __CLASS__, 'render_system_status_page' )  // Callback
		);

		// Settings submenu (admin only)
		if ( current_user_can( 'manage_options' ) ) {
			add_submenu_page(
				'panapana-course-automation',                 // Parent slug
				__( 'Settings', 'panapana' ),                // Page title
				__( 'Settings', 'panapana' ),                // Menu title
				'manage_options',                             // Capability
				'panapana-automation-settings',               // Menu slug
				array( __CLASS__, 'render_settings_page' )   // Callback
			);
		}
	}

	/**
	 * Enqueue admin assets (CSS and JS).
	 *
	 * @param string $hook_suffix The current admin page hook suffix.
	 */
	public static function enqueue_admin_assets( $hook_suffix ) {
		// Only load on our admin pages
		if ( strpos( $hook_suffix, 'panapana-' ) === false ) {
			return;
		}

		// Enqueue admin styles
		wp_enqueue_style(
			'panapana-admin-styles',
			PANAPANA_AUTOMATION_URL . 'admin/assets/admin-styles.css',
			array(),
			PANAPANA_AUTOMATION_VERSION
		);

		// Enqueue admin scripts
		wp_enqueue_script(
			'panapana-admin-scripts',
			PANAPANA_AUTOMATION_URL . 'admin/assets/admin-scripts.js',
			array( 'jquery' ),
			PANAPANA_AUTOMATION_VERSION,
			true
		);

		// Localize script for AJAX
		wp_localize_script( 'panapana-admin-scripts', 'panapana_admin', array(
			'ajax_url' => admin_url( 'admin-ajax.php' ),
			'nonce'    => wp_create_nonce( 'panapana_admin_nonce' ),
			'strings'  => array(
				'uploading'    => __( 'Uploading...', 'panapana' ),
				'processing'   => __( 'Processing...', 'panapana' ),
				'success'      => __( 'Success!', 'panapana' ),
				'error'        => __( 'Error occurred', 'panapana' ),
			),
		) );
	}

	/**
	 * Render the dashboard page.
	 */
	public static function render_dashboard_page() {
		include_once PANAPANA_AUTOMATION_PATH . 'admin/pages/dashboard-page.php';
	}

	/**
	 * Render the CSV upload page.
	 */
	public static function render_csv_upload_page() {
		include_once PANAPANA_AUTOMATION_PATH . 'admin/pages/csv-upload-page.php';
	}

	/**
	 * Render the course management page.
	 */
	public static function render_course_management_page() {
		include_once PANAPANA_AUTOMATION_PATH . 'admin/pages/course-management-page.php';
	}

	/**
	 * Render the system status page.
	 */
	public static function render_system_status_page() {
		include_once PANAPANA_AUTOMATION_PATH . 'admin/pages/system-status-page.php';
	}

	/**
	 * Render the settings page.
	 */
	public static function render_settings_page() {
		include_once PANAPANA_AUTOMATION_PATH . 'admin/pages/settings-page.php';
	}
}

// Initialize the admin menu
Panapana_Admin_Menu::init();
