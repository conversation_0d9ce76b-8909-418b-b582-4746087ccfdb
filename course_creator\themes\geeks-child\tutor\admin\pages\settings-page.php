<?php
/**
 * Panapana Course Automation - Settings Page
 *
 * Configuration interface for the course automation system.
 *
 * @package TutorLMS_CourseAutomation
 * @subpackage Admin
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

// Security check - only administrators can access settings
if ( ! current_user_can( 'manage_options' ) ) {
	wp_die( __( 'You do not have sufficient permissions to access this page.' ) );
}

// Handle form submission
$settings_saved = false;
if ( isset( $_POST['save_settings'] ) && wp_verify_nonce( $_POST['panapana_settings_nonce'], 'panapana_save_settings' ) ) {
	$settings_saved = panapana_save_settings();
}

// Get current settings
$settings = panapana_get_settings();

?>
<div class="panapana-admin-wrap">
	<h1><?php _e( 'Course Automation Settings', 'panapana' ); ?></h1>

	<?php if ( $settings_saved ) : ?>
		<div class="panapana-notice success">
			<p><?php _e( 'Settings saved successfully!', 'panapana' ); ?></p>
		</div>
	<?php endif; ?>

	<form method="post" action="">
		<?php wp_nonce_field( 'panapana_save_settings', 'panapana_settings_nonce' ); ?>

		<div class="panapana-dashboard-grid">
			<!-- Main settings -->
			<div class="panapana-main-content">
				<!-- General Settings -->
				<div class="panapana-dashboard-card">
					<h2><?php _e( 'General Settings', 'panapana' ); ?></h2>
					<table class="form-table">
						<tr>
							<th scope="row"><?php _e( 'Default Course Status', 'panapana' ); ?></th>
							<td>
								<select name="default_course_status">
									<option value="draft" <?php selected( $settings['default_course_status'], 'draft' ); ?>><?php _e( 'Draft', 'panapana' ); ?></option>
									<option value="publish" <?php selected( $settings['default_course_status'], 'publish' ); ?>><?php _e( 'Published', 'panapana' ); ?></option>
									<option value="private" <?php selected( $settings['default_course_status'], 'private' ); ?>><?php _e( 'Private', 'panapana' ); ?></option>
								</select>
								<p class="description"><?php _e( 'Default status for newly created courses.', 'panapana' ); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php _e( 'Course Author', 'panapana' ); ?></th>
							<td>
								<?php
								wp_dropdown_users( array(
									'name' => 'default_course_author',
									'selected' => $settings['default_course_author'],
									'show_option_none' => __( 'Current User', 'panapana' ),
									'option_none_value' => 0,
								) );
								?>
								<p class="description"><?php _e( 'Default author for automated courses. Leave blank to use current user.', 'panapana' ); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php _e( 'Enable Course Thumbnails', 'panapana' ); ?></th>
							<td>
								<label>
									<input type="checkbox" name="enable_thumbnails" value="1" <?php checked( $settings['enable_thumbnails'] ); ?>>
									<?php _e( 'Automatically generate course thumbnails', 'panapana' ); ?>
								</label>
								<p class="description"><?php _e( 'Generate default thumbnails for courses without featured images.', 'panapana' ); ?></p>
							</td>
						</tr>
					</table>
				</div>

				<!-- File Upload Settings -->
				<div class="panapana-dashboard-card">
					<h2><?php _e( 'File Upload Settings', 'panapana' ); ?></h2>
					<table class="form-table">
						<tr>
							<th scope="row"><?php _e( 'Max File Size (MB)', 'panapana' ); ?></th>
							<td>
								<input type="number" name="max_upload_size" value="<?php echo esc_attr( $settings['max_upload_size'] ); ?>" min="1" max="100" step="1">
								<p class="description">
									<?php printf( __( 'Maximum CSV file size. Server limit: %s', 'panapana' ), ini_get( 'upload_max_filesize' ) ); ?>
								</p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php _e( 'Allowed File Types', 'panapana' ); ?></th>
							<td>
								<input type="text" name="allowed_file_types" value="<?php echo esc_attr( $settings['allowed_file_types'] ); ?>" class="regular-text">
								<p class="description"><?php _e( 'Comma-separated list of allowed file extensions (e.g., csv, txt).', 'panapana' ); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php _e( 'Upload Directory', 'panapana' ); ?></th>
							<td>
								<input type="text" name="upload_directory" value="<?php echo esc_attr( $settings['upload_directory'] ); ?>" class="regular-text">
								<p class="description"><?php _e( 'Directory for storing uploaded files (relative to uploads folder).', 'panapana' ); ?></p>
							</td>
						</tr>
					</table>
				</div>

				<!-- Video Settings -->
				<div class="panapana-dashboard-card">
					<h2><?php _e( 'Video Settings', 'panapana' ); ?></h2>
					<table class="form-table">
						<tr>
							<th scope="row"><?php _e( 'VTT Subtitle Path', 'panapana' ); ?></th>
							<td>
								<input type="text" name="vtt_subtitle_path" value="<?php echo esc_attr( $settings['vtt_subtitle_path'] ); ?>" class="regular-text">
								<p class="description"><?php _e( 'Path to VTT subtitle files (relative to uploads folder).', 'panapana' ); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php _e( 'Video Player Settings', 'panapana' ); ?></th>
							<td>
								<label>
									<input type="checkbox" name="enable_video_controls" value="1" <?php checked( $settings['enable_video_controls'] ); ?>>
									<?php _e( 'Show video player controls', 'panapana' ); ?>
								</label><br>
								<label>
									<input type="checkbox" name="enable_video_autoplay" value="1" <?php checked( $settings['enable_video_autoplay'] ); ?>>
									<?php _e( 'Enable video autoplay', 'panapana' ); ?>
								</label><br>
								<label>
									<input type="checkbox" name="enable_video_subtitles" value="1" <?php checked( $settings['enable_video_subtitles'] ); ?>>
									<?php _e( 'Enable subtitle support', 'panapana' ); ?>
								</label>
							</td>
						</tr>
					</table>
				</div>

				<!-- E-book Settings -->
				<div class="panapana-dashboard-card">
					<h2><?php _e( 'E-book Settings', 'panapana' ); ?></h2>
					<table class="form-table">
						<tr>
							<th scope="row"><?php _e( 'PDF Viewer Height', 'panapana' ); ?></th>
							<td>
								<input type="number" name="pdf_viewer_height" value="<?php echo esc_attr( $settings['pdf_viewer_height'] ); ?>" min="300" max="1200" step="50">
								<span>px</span>
								<p class="description"><?php _e( 'Default height for PDF iframe viewers.', 'panapana' ); ?></p>
							</td>
						</tr>
						<tr>
							<th scope="row"><?php _e( 'PDF Viewer Options', 'panapana' ); ?></th>
							<td>
								<label>
									<input type="checkbox" name="pdf_enable_download" value="1" <?php checked( $settings['pdf_enable_download'] ); ?>>
									<?php _e( 'Allow PDF downloads', 'panapana' ); ?>
								</label><br>
								<label>
									<input type="checkbox" name="pdf_enable_print" value="1" <?php checked( $settings['pdf_enable_print'] ); ?>>
									<?php _e( 'Allow PDF printing', 'panapana' ); ?>
								</label>
							</td>
						</tr>
					</table>
				</div>
			</div>

			<!-- Sidebar -->
			<div class="panapana-sidebar">
				<!-- System Information -->
				<div class="panapana-dashboard-card">
					<h2><?php _e( 'System Information', 'panapana' ); ?></h2>
					<ul class="panapana-info-list">
						<li>
							<span class="panapana-info-label"><?php _e( 'Version', 'panapana' ); ?></span>
							<span class="panapana-info-value">v<?php echo esc_html( PANAPANA_AUTOMATION_VERSION ); ?></span>
						</li>
						<li>
							<span class="panapana-info-label"><?php _e( 'Upload Limit', 'panapana' ); ?></span>
							<span class="panapana-info-value"><?php echo esc_html( ini_get( 'upload_max_filesize' ) ); ?></span>
						</li>
						<li>
							<span class="panapana-info-label"><?php _e( 'Memory Limit', 'panapana' ); ?></span>
							<span class="panapana-info-value"><?php echo esc_html( ini_get( 'memory_limit' ) ); ?></span>
						</li>
						<li>
							<span class="panapana-info-label"><?php _e( 'PHP Version', 'panapana' ); ?></span>
							<span class="panapana-info-value"><?php echo esc_html( PHP_VERSION ); ?></span>
						</li>
					</ul>
				</div>

				<!-- User Permissions -->
				<div class="panapana-dashboard-card">
					<h2><?php _e( 'User Permissions', 'panapana' ); ?></h2>
					<p><?php _e( 'Users with course automation access:', 'panapana' ); ?></p>
					<ul class="panapana-info-list">
						<?php
						$automation_users = panapana_get_automation_users();
						foreach ( $automation_users as $user ) :
						?>
							<li>
								<span class="panapana-info-label"><?php echo esc_html( $user->display_name ); ?></span>
								<span class="panapana-info-value"><?php echo esc_html( implode( ', ', $user->roles ) ); ?></span>
							</li>
						<?php endforeach; ?>
					</ul>
				</div>

				<!-- Quick Actions -->
				<div class="panapana-dashboard-card">
					<h2><?php _e( 'Quick Actions', 'panapana' ); ?></h2>
					<div class="panapana-action-buttons">
						<button type="button" onclick="panapanaClearCache()" class="panapana-action-button">
							<span class="dashicons dashicons-update"></span>
							<?php _e( 'Clear Cache', 'panapana' ); ?>
						</button>
						<button type="button" onclick="panapanaResetSettings()" class="panapana-action-button">
							<span class="dashicons dashicons-undo"></span>
							<?php _e( 'Reset to Defaults', 'panapana' ); ?>
						</button>
						<a href="<?php echo admin_url( 'admin.php?page=panapana-system-status' ); ?>" class="panapana-action-button">
							<span class="dashicons dashicons-admin-tools"></span>
							<?php _e( 'System Status', 'panapana' ); ?>
						</a>
					</div>
				</div>

				<!-- Export/Import Settings -->
				<div class="panapana-dashboard-card">
					<h2><?php _e( 'Backup Settings', 'panapana' ); ?></h2>
					<p><?php _e( 'Export or import your settings configuration.', 'panapana' ); ?></p>
					<div class="panapana-action-buttons">
						<button type="button" onclick="panapanaExportSettings()" class="panapana-action-button">
							<span class="dashicons dashicons-download"></span>
							<?php _e( 'Export Settings', 'panapana' ); ?>
						</button>
						<button type="button" onclick="panapanaImportSettings()" class="panapana-action-button">
							<span class="dashicons dashicons-upload"></span>
							<?php _e( 'Import Settings', 'panapana' ); ?>
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Save Button -->
		<div class="panapana-action-buttons" style="margin-top: 20px;">
			<button type="submit" name="save_settings" class="button button-primary button-large">
				<span class="dashicons dashicons-yes"></span>
				<?php _e( 'Save Settings', 'panapana' ); ?>
			</button>
			<a href="<?php echo admin_url( 'admin.php?page=panapana-course-automation' ); ?>" class="button button-large">
				<?php _e( 'Cancel', 'panapana' ); ?>
			</a>
		</div>
	</form>
</div>

<script>
function panapanaClearCache() {
	if (confirm('<?php _e( 'Are you sure you want to clear the cache?', 'panapana' ); ?>')) {
		jQuery.post(panapana_admin.ajax_url, {
			action: 'panapana_clear_cache',
			nonce: panapana_admin.nonce
		}, function(response) {
			if (response.success) {
				alert('<?php _e( 'Cache cleared successfully!', 'panapana' ); ?>');
			} else {
				alert('<?php _e( 'Failed to clear cache.', 'panapana' ); ?>');
			}
		});
	}
}

function panapanaResetSettings() {
	if (confirm('<?php _e( 'Are you sure you want to reset all settings to defaults? This cannot be undone.', 'panapana' ); ?>')) {
		window.location.href = '<?php echo admin_url( 'admin.php?page=panapana-automation-settings&reset=1&nonce=' . wp_create_nonce( 'panapana_reset_settings' ) ); ?>';
	}
}

function panapanaExportSettings() {
	window.location.href = '<?php echo admin_url( 'admin-ajax.php?action=panapana_export_settings&nonce=' . wp_create_nonce( 'panapana_export_settings' ) ); ?>';
}

function panapanaImportSettings() {
	// Create file input
	var input = document.createElement('input');
	input.type = 'file';
	input.accept = '.json';
	input.onchange = function(e) {
		var file = e.target.files[0];
		if (file) {
			var formData = new FormData();
			formData.append('settings_file', file);
			formData.append('action', 'panapana_import_settings');
			formData.append('nonce', panapana_admin.nonce);
			
			jQuery.ajax({
				url: panapana_admin.ajax_url,
				type: 'POST',
				data: formData,
				processData: false,
				contentType: false,
				success: function(response) {
					if (response.success) {
						alert('<?php _e( 'Settings imported successfully! Page will reload.', 'panapana' ); ?>');
						location.reload();
					} else {
						alert('<?php _e( 'Failed to import settings:', 'panapana' ); ?> ' + response.data.message);
					}
				}
			});
		}
	};
	input.click();
}
</script>

<?php
/**
 * Get current settings with defaults
 */
function panapana_get_settings() {
	$defaults = array(
		'default_course_status' => 'draft',
		'default_course_author' => 0,
		'enable_thumbnails' => 1,
		'max_upload_size' => 10,
		'allowed_file_types' => 'csv',
		'upload_directory' => 'panapana-uploads',
		'vtt_subtitle_path' => 'vtt',
		'enable_video_controls' => 1,
		'enable_video_autoplay' => 0,
		'enable_video_subtitles' => 1,
		'pdf_viewer_height' => 600,
		'pdf_enable_download' => 1,
		'pdf_enable_print' => 1,
	);
	
	$settings = get_option( 'panapana_automation_settings', array() );
	return wp_parse_args( $settings, $defaults );
}

/**
 * Save settings
 */
function panapana_save_settings() {
	$settings = array(
		'default_course_status' => sanitize_text_field( $_POST['default_course_status'] ?? 'draft' ),
		'default_course_author' => intval( $_POST['default_course_author'] ?? 0 ),
		'enable_thumbnails' => isset( $_POST['enable_thumbnails'] ) ? 1 : 0,
		'max_upload_size' => intval( $_POST['max_upload_size'] ?? 10 ),
		'allowed_file_types' => sanitize_text_field( $_POST['allowed_file_types'] ?? 'csv' ),
		'upload_directory' => sanitize_text_field( $_POST['upload_directory'] ?? 'panapana-uploads' ),
		'vtt_subtitle_path' => sanitize_text_field( $_POST['vtt_subtitle_path'] ?? 'vtt' ),
		'enable_video_controls' => isset( $_POST['enable_video_controls'] ) ? 1 : 0,
		'enable_video_autoplay' => isset( $_POST['enable_video_autoplay'] ) ? 1 : 0,
		'enable_video_subtitles' => isset( $_POST['enable_video_subtitles'] ) ? 1 : 0,
		'pdf_viewer_height' => intval( $_POST['pdf_viewer_height'] ?? 600 ),
		'pdf_enable_download' => isset( $_POST['pdf_enable_download'] ) ? 1 : 0,
		'pdf_enable_print' => isset( $_POST['pdf_enable_print'] ) ? 1 : 0,
	);
	
	return update_option( 'panapana_automation_settings', $settings );
}

/**
 * Get users with automation access
 */
function panapana_get_automation_users() {
	$users = get_users( array(
		'meta_query' => array(
			'relation' => 'OR',
			array(
				'key' => 'wp_capabilities',
				'value' => 'export_course_data',
				'compare' => 'LIKE'
			),
			array(
				'key' => 'wp_capabilities',
				'value' => 'manage_options',
				'compare' => 'LIKE'
			)
		)
	) );
	
	return $users;
}
?>
