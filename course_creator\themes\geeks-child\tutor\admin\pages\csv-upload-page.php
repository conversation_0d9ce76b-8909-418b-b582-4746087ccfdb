<?php
/**
 * Panapana Course Automation - CSV Upload Page
 *
 * Professional CSV upload interface for course creation.
 *
 * @package TutorLMS_CourseAutomation
 * @subpackage Admin
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

// Security check
if ( ! panapana_user_can_access_automation() ) {
	wp_die( __( 'You do not have sufficient permissions to access this page.' ) );
}

// Handle form submission
$upload_result = null;
if ( isset( $_POST['panapana_csv_upload'] ) && wp_verify_nonce( $_POST['panapana_nonce'], 'panapana_csv_upload' ) ) {
	$upload_result = panapana_handle_csv_upload();
}

?>
<div class="panapana-admin-wrap">
	<h1><?php _e( 'Upload CSV Course Data', 'panapana' ); ?></h1>

	<?php if ( $upload_result ) : ?>
		<?php if ( $upload_result['success'] ) : ?>
			<div class="panapana-notice success">
				<p><strong><?php _e( 'Success!', 'panapana' ); ?></strong> <?php echo esc_html( $upload_result['message'] ); ?></p>
				<?php if ( ! empty( $upload_result['courses'] ) ) : ?>
					<p><?php _e( 'Created courses:', 'panapana' ); ?></p>
					<ul>
						<?php foreach ( $upload_result['courses'] as $course ) : ?>
							<li>
								<a href="<?php echo esc_url( admin_url( 'post.php?post=' . $course['id'] . '&action=edit' ) ); ?>">
									<?php echo esc_html( $course['title'] ); ?>
								</a>
							</li>
						<?php endforeach; ?>
					</ul>
				<?php endif; ?>
			</div>
		<?php else : ?>
			<div class="panapana-notice error">
				<p><strong><?php _e( 'Error:', 'panapana' ); ?></strong> <?php echo esc_html( $upload_result['message'] ); ?></p>
				<?php if ( ! empty( $upload_result['details'] ) ) : ?>
					<details>
						<summary><?php _e( 'Error Details', 'panapana' ); ?></summary>
						<pre><?php echo esc_html( $upload_result['details'] ); ?></pre>
					</details>
				<?php endif; ?>
			</div>
		<?php endif; ?>
	<?php endif; ?>

	<div class="panapana-dashboard-grid">
		<!-- Upload form -->
		<div class="panapana-main-content">
			<div class="panapana-dashboard-card">
				<h2><?php _e( 'Upload CSV File', 'panapana' ); ?></h2>
				<p><?php _e( 'Select a CSV file containing course data to automatically create courses in Tutor LMS.', 'panapana' ); ?></p>

				<form id="panapana-csv-form" method="post" enctype="multipart/form-data">
					<?php wp_nonce_field( 'panapana_csv_upload', 'panapana_nonce' ); ?>
					
					<div class="panapana-upload-area" id="panapana-upload-area">
						<span class="dashicons dashicons-upload"></span>
						<div class="panapana-upload-text"><?php _e( 'Click to select CSV file or drag and drop', 'panapana' ); ?></div>
						<div class="panapana-upload-hint"><?php _e( 'Maximum file size: 10MB. Only CSV files are allowed.', 'panapana' ); ?></div>
						<input type="file" id="panapana-csv-file" name="csv_file" accept=".csv" style="display: none;">
					</div>

					<div class="panapana-progress-container">
						<div class="panapana-progress-bar">
							<div class="panapana-progress-fill"></div>
						</div>
						<div class="panapana-progress-text"></div>
					</div>

					<div class="panapana-upload-options" style="margin-top: 20px;">
						<h3><?php _e( 'Upload Options', 'panapana' ); ?></h3>
						<table class="form-table">
							<tr>
								<th scope="row"><?php _e( 'Course Status', 'panapana' ); ?></th>
								<td>
									<select name="course_status">
										<option value="publish"><?php _e( 'Published', 'panapana' ); ?></option>
										<option value="draft" selected><?php _e( 'Draft', 'panapana' ); ?></option>
									</select>
									<p class="description"><?php _e( 'Set the initial status for created courses.', 'panapana' ); ?></p>
								</td>
							</tr>
							<tr>
								<th scope="row"><?php _e( 'Overwrite Existing', 'panapana' ); ?></th>
								<td>
									<label>
										<input type="checkbox" name="overwrite_existing" value="1">
										<?php _e( 'Overwrite courses with the same title', 'panapana' ); ?>
									</label>
									<p class="description"><?php _e( 'If checked, existing courses with the same title will be updated.', 'panapana' ); ?></p>
								</td>
							</tr>
							<tr>
								<th scope="row"><?php _e( 'Validation Mode', 'panapana' ); ?></th>
								<td>
									<label>
										<input type="radio" name="validation_mode" value="strict" checked>
										<?php _e( 'Strict - Stop on first error', 'panapana' ); ?>
									</label><br>
									<label>
										<input type="radio" name="validation_mode" value="lenient">
										<?php _e( 'Lenient - Skip invalid rows and continue', 'panapana' ); ?>
									</label>
									<p class="description"><?php _e( 'Choose how to handle validation errors.', 'panapana' ); ?></p>
								</td>
							</tr>
						</table>
					</div>

					<div class="panapana-action-buttons">
						<button type="submit" id="panapana-submit-csv" name="panapana_csv_upload" class="button button-primary" disabled>
							<span class="dashicons dashicons-upload"></span>
							<?php _e( 'Process CSV', 'panapana' ); ?>
						</button>
						<a href="<?php echo admin_url( 'admin.php?page=panapana-course-automation' ); ?>" class="button">
							<?php _e( 'Cancel', 'panapana' ); ?>
						</a>
					</div>
				</form>
			</div>
		</div>

		<!-- Sidebar with help -->
		<div class="panapana-sidebar">
			<div class="panapana-dashboard-card">
				<h2><?php _e( 'CSV Format Guide', 'panapana' ); ?></h2>
				<p><?php _e( 'Your CSV file should contain the following columns:', 'panapana' ); ?></p>
				<ul>
					<li><strong>course_title</strong> - <?php _e( 'Course name', 'panapana' ); ?></li>
					<li><strong>course_description</strong> - <?php _e( 'Course description', 'panapana' ); ?></li>
					<li><strong>topic_title</strong> - <?php _e( 'Topic/module name', 'panapana' ); ?></li>
					<li><strong>lesson_title</strong> - <?php _e( 'Lesson name', 'panapana' ); ?></li>
					<li><strong>lesson_type</strong> - <?php _e( 'video, ebook, or quiz', 'panapana' ); ?></li>
					<li><strong>content</strong> - <?php _e( 'Lesson content or video URL', 'panapana' ); ?></li>
				</ul>
				
				<div class="panapana-action-buttons">
					<a href="<?php echo esc_url( panapana_get_csv_template_url() ); ?>" class="panapana-action-button" download>
						<span class="dashicons dashicons-download"></span>
						<?php _e( 'Download Template', 'panapana' ); ?>
					</a>
				</div>
			</div>

			<div class="panapana-dashboard-card">
				<h2><?php _e( 'Upload Tips', 'panapana' ); ?></h2>
				<ul>
					<li><?php _e( 'Use UTF-8 encoding for special characters', 'panapana' ); ?></li>
					<li><?php _e( 'Keep file size under 10MB', 'panapana' ); ?></li>
					<li><?php _e( 'Test with a small file first', 'panapana' ); ?></li>
					<li><?php _e( 'Backup your site before large uploads', 'panapana' ); ?></li>
				</ul>
			</div>

			<div class="panapana-dashboard-card">
				<h2><?php _e( 'Recent Uploads', 'panapana' ); ?></h2>
				<?php
				$recent_uploads = panapana_get_recent_uploads( 3 );
				if ( ! empty( $recent_uploads ) ) :
				?>
					<ul class="panapana-info-list">
						<?php foreach ( $recent_uploads as $upload ) : ?>
							<li>
								<span class="panapana-info-label"><?php echo esc_html( $upload['filename'] ); ?></span>
								<span class="panapana-info-value"><?php echo esc_html( $upload['date'] ); ?></span>
							</li>
						<?php endforeach; ?>
					</ul>
				<?php else : ?>
					<p><?php _e( 'No recent uploads found.', 'panapana' ); ?></p>
				<?php endif; ?>
			</div>
		</div>
	</div>
</div>

<?php
/**
 * Handle CSV upload processing
 */
function panapana_handle_csv_upload() {
	// Validate file upload
	if ( ! isset( $_FILES['csv_file'] ) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK ) {
		return array(
			'success' => false,
			'message' => __( 'No file uploaded or upload error occurred.', 'panapana' ),
		);
	}

	$file = $_FILES['csv_file'];
	
	// Validate file type
	$file_extension = strtolower( pathinfo( $file['name'], PATHINFO_EXTENSION ) );
	if ( $file_extension !== 'csv' ) {
		return array(
			'success' => false,
			'message' => __( 'Please upload a CSV file.', 'panapana' ),
		);
	}

	// Validate file size (10MB limit)
	if ( $file['size'] > 10 * 1024 * 1024 ) {
		return array(
			'success' => false,
			'message' => __( 'File size must be less than 10MB.', 'panapana' ),
		);
	}

	try {
		// Process the CSV file using existing Phase 2 functionality
		$csv_data = panapana_read_csv_file( $file['tmp_name'] );
		$grouped_data = panapana_group_csv_data_by_course( $csv_data );
		
		$created_courses = array();
		$options = array(
			'course_status' => sanitize_text_field( $_POST['course_status'] ?? 'draft' ),
			'overwrite_existing' => isset( $_POST['overwrite_existing'] ),
			'validation_mode' => sanitize_text_field( $_POST['validation_mode'] ?? 'strict' ),
		);

		foreach ( $grouped_data as $course_data ) {
			$course_id = panapana_create_course_from_data( $course_data, $options );
			
			if ( ! is_wp_error( $course_id ) ) {
				// Mark as automated course
				update_post_meta( $course_id, '_panapana_automated', '1' );
				update_post_meta( $course_id, '_panapana_upload_date', current_time( 'mysql' ) );
				update_post_meta( $course_id, '_panapana_upload_filename', sanitize_file_name( $file['name'] ) );
				
				$created_courses[] = array(
					'id' => $course_id,
					'title' => get_the_title( $course_id ),
				);
			} else {
				throw new Exception( $course_id->get_error_message() );
			}
		}

		// Log successful upload
		panapana_log_upload_activity( $file['name'], count( $created_courses ), true );

		return array(
			'success' => true,
			'message' => sprintf( 
				_n( 
					'Successfully created %d course.', 
					'Successfully created %d courses.', 
					count( $created_courses ), 
					'panapana' 
				), 
				count( $created_courses ) 
			),
			'courses' => $created_courses,
		);

	} catch ( Exception $e ) {
		// Log failed upload
		panapana_log_upload_activity( $file['name'], 0, false, $e->getMessage() );

		return array(
			'success' => false,
			'message' => __( 'Failed to process CSV file.', 'panapana' ),
			'details' => $e->getMessage(),
		);
	}
}

/**
 * Get CSV template download URL
 */
function panapana_get_csv_template_url() {
	return admin_url( 'admin-ajax.php?action=panapana_download_csv_template&nonce=' . wp_create_nonce( 'panapana_csv_template' ) );
}

/**
 * Get recent upload history
 */
function panapana_get_recent_uploads( $limit = 5 ) {
	$uploads = get_option( 'panapana_upload_history', array() );
	return array_slice( $uploads, 0, $limit );
}

/**
 * Log upload activity
 */
function panapana_log_upload_activity( $filename, $courses_created, $success, $error_message = '' ) {
	$uploads = get_option( 'panapana_upload_history', array() );
	
	array_unshift( $uploads, array(
		'filename' => $filename,
		'date' => current_time( 'M j, Y g:i A' ),
		'courses_created' => $courses_created,
		'success' => $success,
		'error_message' => $error_message,
		'user_id' => get_current_user_id(),
	) );
	
	// Keep only last 50 uploads
	$uploads = array_slice( $uploads, 0, 50 );
	
	update_option( 'panapana_upload_history', $uploads );
}
?>
