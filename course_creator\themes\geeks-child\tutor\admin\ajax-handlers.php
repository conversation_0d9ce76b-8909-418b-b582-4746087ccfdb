<?php
/**
 * Panapana Course Automation - AJAX Handlers
 *
 * Handles AJAX requests for the admin interface.
 *
 * @package TutorLMS_CourseAutomation
 * @subpackage Admin
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Initialize AJAX handlers
 */
function panapana_init_ajax_handlers() {
	// CSV processing
	add_action( 'wp_ajax_panapana_process_csv', 'panapana_ajax_process_csv' );
	
	// System status
	add_action( 'wp_ajax_panapana_refresh_system_status', 'panapana_ajax_refresh_system_status' );
	add_action( 'wp_ajax_panapana_export_system_info', 'panapana_ajax_export_system_info' );
	
	// Course export
	add_action( 'wp_ajax_panapana_export_courses', 'panapana_ajax_export_courses' );
	add_action( 'wp_ajax_panapana_export_single_course', 'panapana_ajax_export_single_course' );
	
	// CSV template download
	add_action( 'wp_ajax_panapana_download_csv_template', 'panapana_ajax_download_csv_template' );
	
	// Settings
	add_action( 'wp_ajax_panapana_clear_cache', 'panapana_ajax_clear_cache' );
	add_action( 'wp_ajax_panapana_export_settings', 'panapana_ajax_export_settings' );
	add_action( 'wp_ajax_panapana_import_settings', 'panapana_ajax_import_settings' );
}
add_action( 'init', 'panapana_init_ajax_handlers' );

/**
 * AJAX handler for CSV processing
 */
function panapana_ajax_process_csv() {
	// Verify nonce and permissions
	if ( ! wp_verify_nonce( $_POST['nonce'], 'panapana_admin_nonce' ) || ! panapana_user_can_access_automation() ) {
		wp_die( json_encode( array( 'success' => false, 'data' => array( 'message' => 'Permission denied' ) ) ) );
	}
	
	try {
		// Process the uploaded file
		$result = panapana_handle_csv_upload();
		
		if ( $result['success'] ) {
			wp_send_json_success( array(
				'message' => $result['message'],
				'courses' => $result['courses'] ?? array(),
				'redirect' => admin_url( 'admin.php?page=panapana-course-management' ),
			) );
		} else {
			wp_send_json_error( array(
				'message' => $result['message'],
				'details' => $result['details'] ?? '',
			) );
		}
	} catch ( Exception $e ) {
		wp_send_json_error( array(
			'message' => __( 'An error occurred while processing the CSV file.', 'panapana' ),
			'details' => $e->getMessage(),
		) );
	}
}

/**
 * AJAX handler for refreshing system status
 */
function panapana_ajax_refresh_system_status() {
	// Verify nonce and permissions
	if ( ! wp_verify_nonce( $_POST['nonce'], 'panapana_admin_nonce' ) || ! panapana_user_can_access_automation() ) {
		wp_die( json_encode( array( 'success' => false, 'data' => array( 'message' => 'Permission denied' ) ) ) );
	}
	
	// Get fresh system information
	$system_info = panapana_get_comprehensive_system_info();
	
	// Generate HTML for system info sections
	ob_start();
	include PANAPANA_AUTOMATION_PATH . 'admin/partials/system-info-sections.php';
	$html = ob_get_clean();
	
	wp_send_json_success( array( 'html' => $html ) );
}

/**
 * AJAX handler for exporting system information
 */
function panapana_ajax_export_system_info() {
	// Verify nonce and permissions
	if ( ! wp_verify_nonce( $_GET['nonce'], 'panapana_admin_nonce' ) || ! panapana_user_can_access_automation() ) {
		wp_die( 'Permission denied' );
	}
	
	$system_info = panapana_get_comprehensive_system_info();
	
	// Add additional debug information
	$system_info['export_info'] = array(
		'exported_at' => current_time( 'mysql' ),
		'exported_by' => wp_get_current_user()->display_name,
		'site_url' => get_site_url(),
	);
	
	// Set headers for download
	header( 'Content-Type: application/json' );
	header( 'Content-Disposition: attachment; filename="panapana-system-info-' . date( 'Y-m-d-H-i-s' ) . '.json"' );
	
	echo json_encode( $system_info, JSON_PRETTY_PRINT );
	exit;
}

/**
 * AJAX handler for exporting multiple courses
 */
function panapana_ajax_export_courses() {
	// Verify nonce and permissions
	if ( ! wp_verify_nonce( $_GET['nonce'], 'panapana_export_courses' ) || ! panapana_user_can_access_automation() ) {
		wp_die( 'Permission denied' );
	}
	
	$course_ids = array_map( 'intval', explode( ',', $_GET['course_ids'] ) );
	
	if ( empty( $course_ids ) ) {
		wp_die( 'No courses selected' );
	}
	
	// Export courses data
	$export_data = array();
	foreach ( $course_ids as $course_id ) {
		$course_data = panapana_export_course_data( $course_id );
		if ( $course_data ) {
			$export_data[] = $course_data;
		}
	}
	
	// Set headers for download
	header( 'Content-Type: text/csv' );
	header( 'Content-Disposition: attachment; filename="courses-export-' . date( 'Y-m-d-H-i-s' ) . '.csv"' );
	
	// Output CSV
	$output = fopen( 'php://output', 'w' );
	
	// CSV headers
	$headers = array(
		'course_title', 'course_description', 'topic_title', 'lesson_title', 
		'lesson_type', 'content', 'quiz_question', 'quiz_answers'
	);
	fputcsv( $output, $headers );
	
	// CSV data
	foreach ( $export_data as $course ) {
		foreach ( $course['rows'] as $row ) {
			fputcsv( $output, $row );
		}
	}
	
	fclose( $output );
	exit;
}

/**
 * AJAX handler for exporting single course
 */
function panapana_ajax_export_single_course() {
	// Verify nonce and permissions
	if ( ! wp_verify_nonce( $_GET['nonce'], 'panapana_export_single' ) || ! panapana_user_can_access_automation() ) {
		wp_die( 'Permission denied' );
	}
	
	$course_id = intval( $_GET['course_id'] );
	
	if ( ! $course_id ) {
		wp_die( 'Invalid course ID' );
	}
	
	// Export course data
	$course_data = panapana_export_course_data( $course_id );
	
	if ( ! $course_data ) {
		wp_die( 'Failed to export course data' );
	}
	
	$course_title = sanitize_file_name( get_the_title( $course_id ) );
	
	// Set headers for download
	header( 'Content-Type: text/csv' );
	header( 'Content-Disposition: attachment; filename="' . $course_title . '-export-' . date( 'Y-m-d-H-i-s' ) . '.csv"' );
	
	// Output CSV
	$output = fopen( 'php://output', 'w' );
	
	// CSV headers
	$headers = array(
		'course_title', 'course_description', 'topic_title', 'lesson_title', 
		'lesson_type', 'content', 'quiz_question', 'quiz_answers'
	);
	fputcsv( $output, $headers );
	
	// CSV data
	foreach ( $course_data['rows'] as $row ) {
		fputcsv( $output, $row );
	}
	
	fclose( $output );
	exit;
}

/**
 * AJAX handler for downloading CSV template
 */
function panapana_ajax_download_csv_template() {
	// Verify nonce and permissions
	if ( ! wp_verify_nonce( $_GET['nonce'], 'panapana_csv_template' ) || ! panapana_user_can_access_automation() ) {
		wp_die( 'Permission denied' );
	}
	
	// Set headers for download
	header( 'Content-Type: text/csv' );
	header( 'Content-Disposition: attachment; filename="panapana-course-template.csv"' );
	
	// Output CSV template
	$output = fopen( 'php://output', 'w' );
	
	// CSV headers
	$headers = array(
		'course_title', 'course_description', 'topic_title', 'lesson_title', 
		'lesson_type', 'content', 'quiz_question', 'quiz_answers'
	);
	fputcsv( $output, $headers );
	
	// Sample data
	$sample_rows = array(
		array(
			'My Sample Course',
			'This is a sample course description',
			'Introduction',
			'Welcome Video',
			'video',
			'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
			'',
			''
		),
		array(
			'My Sample Course',
			'',
			'Introduction',
			'Course Overview',
			'ebook',
			'https://example.com/course-overview.pdf',
			'',
			''
		),
		array(
			'My Sample Course',
			'',
			'Introduction',
			'Knowledge Check',
			'quiz',
			'',
			'What is the main topic of this course?',
			'Course automation|Manual course creation|Video editing|Web design'
		),
	);
	
	foreach ( $sample_rows as $row ) {
		fputcsv( $output, $row );
	}
	
	fclose( $output );
	exit;
}

/**
 * AJAX handler for clearing cache
 */
function panapana_ajax_clear_cache() {
	// Verify nonce and permissions
	if ( ! wp_verify_nonce( $_POST['nonce'], 'panapana_admin_nonce' ) || ! current_user_can( 'manage_options' ) ) {
		wp_send_json_error( array( 'message' => 'Permission denied' ) );
	}
	
	// Clear various caches
	$cleared = array();
	
	// WordPress object cache
	if ( function_exists( 'wp_cache_flush' ) ) {
		wp_cache_flush();
		$cleared[] = 'Object cache';
	}
	
	// Transients
	global $wpdb;
	$wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_panapana_%'" );
	$wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_panapana_%'" );
	$cleared[] = 'Transients';
	
	// Clear upload history cache
	delete_option( 'panapana_upload_history_cache' );
	$cleared[] = 'Upload history';
	
	wp_send_json_success( array( 
		'message' => sprintf( __( 'Cache cleared: %s', 'panapana' ), implode( ', ', $cleared ) )
	) );
}

/**
 * AJAX handler for exporting settings
 */
function panapana_ajax_export_settings() {
	// Verify nonce and permissions
	if ( ! wp_verify_nonce( $_GET['nonce'], 'panapana_export_settings' ) || ! current_user_can( 'manage_options' ) ) {
		wp_die( 'Permission denied' );
	}
	
	$settings = get_option( 'panapana_automation_settings', array() );
	
	// Add export metadata
	$export_data = array(
		'version' => PANAPANA_AUTOMATION_VERSION,
		'exported_at' => current_time( 'mysql' ),
		'exported_by' => wp_get_current_user()->display_name,
		'site_url' => get_site_url(),
		'settings' => $settings,
	);
	
	// Set headers for download
	header( 'Content-Type: application/json' );
	header( 'Content-Disposition: attachment; filename="panapana-settings-' . date( 'Y-m-d-H-i-s' ) . '.json"' );
	
	echo json_encode( $export_data, JSON_PRETTY_PRINT );
	exit;
}

/**
 * AJAX handler for importing settings
 */
function panapana_ajax_import_settings() {
	// Verify nonce and permissions
	if ( ! wp_verify_nonce( $_POST['nonce'], 'panapana_admin_nonce' ) || ! current_user_can( 'manage_options' ) ) {
		wp_send_json_error( array( 'message' => 'Permission denied' ) );
	}
	
	// Check if file was uploaded
	if ( ! isset( $_FILES['settings_file'] ) || $_FILES['settings_file']['error'] !== UPLOAD_ERR_OK ) {
		wp_send_json_error( array( 'message' => 'No file uploaded or upload error' ) );
	}
	
	// Read and validate file
	$file_content = file_get_contents( $_FILES['settings_file']['tmp_name'] );
	$import_data = json_decode( $file_content, true );
	
	if ( json_last_error() !== JSON_ERROR_NONE ) {
		wp_send_json_error( array( 'message' => 'Invalid JSON file' ) );
	}
	
	if ( ! isset( $import_data['settings'] ) ) {
		wp_send_json_error( array( 'message' => 'Invalid settings file format' ) );
	}
	
	// Import settings
	$result = update_option( 'panapana_automation_settings', $import_data['settings'] );
	
	if ( $result ) {
		wp_send_json_success( array( 'message' => 'Settings imported successfully' ) );
	} else {
		wp_send_json_error( array( 'message' => 'Failed to import settings' ) );
	}
}
?>
