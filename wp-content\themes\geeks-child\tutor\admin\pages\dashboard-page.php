<?php
/**
 * Panapana Course Automation - Dashboard Page
 *
 * Main dashboard page for the course automation admin interface.
 *
 * @package TutorLMS_CourseAutomation
 * @subpackage Admin
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

// Security check
if ( ! panapana_user_can_access_automation() ) {
	wp_die( __( 'You do not have sufficient permissions to access this page.' ) );
}

// Get system information
$system_info = panapana_get_automation_info();

// Get course statistics
$course_stats = panapana_get_course_statistics();

?>
<div class="panapana-admin-wrap">
	<h1><?php _e( 'Course Automation Dashboard', 'panapana' ); ?></h1>

	<div class="panapana-dashboard-grid">
		<!-- Main content area -->
		<div class="panapana-main-content">
			<!-- Quick stats -->
			<div class="panapana-dashboard-card">
				<h2><?php _e( 'Quick Statistics', 'panapana' ); ?></h2>
				<div class="panapana-quick-stats">
					<div class="panapana-stat-item">
						<span class="panapana-stat-number"><?php echo esc_html( $course_stats['total_courses'] ); ?></span>
						<span class="panapana-stat-label"><?php _e( 'Total Courses', 'panapana' ); ?></span>
					</div>
					<div class="panapana-stat-item">
						<span class="panapana-stat-number"><?php echo esc_html( $course_stats['automated_courses'] ); ?></span>
						<span class="panapana-stat-label"><?php _e( 'Automated Courses', 'panapana' ); ?></span>
					</div>
					<div class="panapana-stat-item">
						<span class="panapana-stat-number"><?php echo esc_html( $course_stats['this_month'] ); ?></span>
						<span class="panapana-stat-label"><?php _e( 'This Month', 'panapana' ); ?></span>
					</div>
					<div class="panapana-stat-item">
						<span class="panapana-stat-number"><?php echo esc_html( $course_stats['success_rate'] ); ?>%</span>
						<span class="panapana-stat-label"><?php _e( 'Success Rate', 'panapana' ); ?></span>
					</div>
				</div>
			</div>

			<!-- Recent activity -->
			<div class="panapana-dashboard-card">
				<h2><?php _e( 'Recent Activity', 'panapana' ); ?></h2>
				<?php
				$recent_courses = panapana_get_recent_automated_courses( 5 );
				if ( ! empty( $recent_courses ) ) :
				?>
					<table class="panapana-course-table">
						<thead>
							<tr>
								<th><?php _e( 'Course', 'panapana' ); ?></th>
								<th><?php _e( 'Created', 'panapana' ); ?></th>
								<th><?php _e( 'Status', 'panapana' ); ?></th>
								<th><?php _e( 'Actions', 'panapana' ); ?></th>
							</tr>
						</thead>
						<tbody>
							<?php foreach ( $recent_courses as $course ) : ?>
								<tr>
									<td>
										<strong><?php echo esc_html( $course['title'] ); ?></strong>
										<br>
										<small><?php echo esc_html( $course['topics_count'] ); ?> <?php _e( 'topics', 'panapana' ); ?></small>
									</td>
									<td><?php echo esc_html( $course['created_date'] ); ?></td>
									<td>
										<span class="panapana-status-indicator <?php echo esc_attr( $course['status_class'] ); ?>"></span>
										<?php echo esc_html( $course['status_text'] ); ?>
									</td>
									<td>
										<a href="<?php echo esc_url( $course['edit_url'] ); ?>" class="button button-small">
											<?php _e( 'View', 'panapana' ); ?>
										</a>
									</td>
								</tr>
							<?php endforeach; ?>
						</tbody>
					</table>
				<?php else : ?>
					<p><?php _e( 'No automated courses found. Start by uploading a CSV file.', 'panapana' ); ?></p>
				<?php endif; ?>
			</div>

			<!-- Quick actions -->
			<div class="panapana-dashboard-card">
				<h2><?php _e( 'Quick Actions', 'panapana' ); ?></h2>
				<div class="panapana-action-buttons">
					<a href="<?php echo admin_url( 'admin.php?page=panapana-csv-upload' ); ?>" class="panapana-action-button">
						<span class="dashicons dashicons-upload"></span>
						<?php _e( 'Upload CSV', 'panapana' ); ?>
					</a>
					<a href="<?php echo admin_url( 'admin.php?page=panapana-course-management' ); ?>" class="panapana-action-button">
						<span class="dashicons dashicons-list-view"></span>
						<?php _e( 'Manage Courses', 'panapana' ); ?>
					</a>
					<a href="<?php echo admin_url( 'admin.php?page=panapana-system-status' ); ?>" class="panapana-action-button">
						<span class="dashicons dashicons-admin-tools"></span>
						<?php _e( 'System Status', 'panapana' ); ?>
					</a>
					<?php if ( current_user_can( 'manage_options' ) ) : ?>
						<a href="<?php echo admin_url( 'admin.php?page=panapana-automation-settings' ); ?>" class="panapana-action-button">
							<span class="dashicons dashicons-admin-settings"></span>
							<?php _e( 'Settings', 'panapana' ); ?>
						</a>
					<?php endif; ?>
				</div>
			</div>
		</div>

		<!-- Sidebar -->
		<div class="panapana-sidebar">
			<!-- System status overview -->
			<div class="panapana-dashboard-card">
				<h2><?php _e( 'System Status', 'panapana' ); ?></h2>
				<ul class="panapana-info-list">
					<li>
						<span class="panapana-info-label"><?php _e( 'WordPress', 'panapana' ); ?></span>
						<span class="panapana-info-value">
							<span class="panapana-status-indicator success"></span>
							<?php echo esc_html( $system_info['wordpress_version'] ); ?>
						</span>
					</li>
					<li>
						<span class="panapana-info-label"><?php _e( 'Tutor LMS', 'panapana' ); ?></span>
						<span class="panapana-info-value">
							<span class="panapana-status-indicator <?php echo $system_info['tutor_active'] ? 'success' : 'error'; ?>"></span>
							<?php echo $system_info['tutor_active'] ? __( 'Active', 'panapana' ) : __( 'Inactive', 'panapana' ); ?>
						</span>
					</li>
					<li>
						<span class="panapana-info-label"><?php _e( 'Automation', 'panapana' ); ?></span>
						<span class="panapana-info-value">
							<span class="panapana-status-indicator success"></span>
							v<?php echo esc_html( $system_info['version'] ); ?>
						</span>
					</li>
					<li>
						<span class="panapana-info-label"><?php _e( 'User Role', 'panapana' ); ?></span>
						<span class="panapana-info-value">
							<?php echo esc_html( implode( ', ', wp_get_current_user()->roles ) ); ?>
						</span>
					</li>
				</ul>
			</div>

			<!-- Help and documentation -->
			<div class="panapana-dashboard-card">
				<h2><?php _e( 'Help & Documentation', 'panapana' ); ?></h2>
				<p><?php _e( 'Need help getting started?', 'panapana' ); ?></p>
				<ul>
					<li><a href="#" target="_blank"><?php _e( 'CSV Format Guide', 'panapana' ); ?></a></li>
					<li><a href="#" target="_blank"><?php _e( 'Video Tutorial', 'panapana' ); ?></a></li>
					<li><a href="#" target="_blank"><?php _e( 'Troubleshooting', 'panapana' ); ?></a></li>
				</ul>
				<div class="panapana-action-buttons">
					<a href="<?php echo admin_url( 'admin.php?page=panapana-csv-upload' ); ?>" class="panapana-action-button">
						<?php _e( 'Download CSV Template', 'panapana' ); ?>
					</a>
				</div>
			</div>
		</div>
	</div>
</div>

<?php
/**
 * Get course statistics for dashboard
 */
function panapana_get_course_statistics() {
	global $wpdb;
	
	// Get total courses
	$total_courses = wp_count_posts( 'courses' )->publish;
	
	// Get automated courses (courses with our automation metadata)
	$automated_courses = $wpdb->get_var( "
		SELECT COUNT(DISTINCT post_id) 
		FROM {$wpdb->postmeta} 
		WHERE meta_key = '_panapana_automated' 
		AND meta_value = '1'
	" );
	
	// Get courses created this month
	$this_month = $wpdb->get_var( $wpdb->prepare( "
		SELECT COUNT(*) 
		FROM {$wpdb->posts} p
		INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
		WHERE p.post_type = 'courses' 
		AND p.post_status = 'publish'
		AND pm.meta_key = '_panapana_automated'
		AND pm.meta_value = '1'
		AND p.post_date >= %s
	", date( 'Y-m-01' ) ) );
	
	// Calculate success rate (simplified - could be more sophisticated)
	$success_rate = $automated_courses > 0 ? round( ( $automated_courses / max( $total_courses, 1 ) ) * 100 ) : 0;
	
	return array(
		'total_courses'    => (int) $total_courses,
		'automated_courses' => (int) $automated_courses,
		'this_month'       => (int) $this_month,
		'success_rate'     => (int) $success_rate,
	);
}

/**
 * Get recent automated courses for dashboard
 */
function panapana_get_recent_automated_courses( $limit = 5 ) {
	global $wpdb;
	
	$courses = $wpdb->get_results( $wpdb->prepare( "
		SELECT p.ID, p.post_title, p.post_date, p.post_status
		FROM {$wpdb->posts} p
		INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
		WHERE p.post_type = 'courses'
		AND pm.meta_key = '_panapana_automated'
		AND pm.meta_value = '1'
		ORDER BY p.post_date DESC
		LIMIT %d
	", $limit ) );
	
	$recent_courses = array();
	foreach ( $courses as $course ) {
		// Get topic count
		$topics_count = $wpdb->get_var( $wpdb->prepare( "
			SELECT COUNT(*) 
			FROM {$wpdb->posts} 
			WHERE post_type = 'topics' 
			AND post_parent = %d 
			AND post_status = 'publish'
		", $course->ID ) );
		
		$recent_courses[] = array(
			'id'           => $course->ID,
			'title'        => $course->post_title,
			'created_date' => mysql2date( 'M j, Y', $course->post_date ),
			'topics_count' => (int) $topics_count,
			'status_class' => $course->post_status === 'publish' ? 'success' : 'warning',
			'status_text'  => $course->post_status === 'publish' ? __( 'Published', 'panapana' ) : __( 'Draft', 'panapana' ),
			'edit_url'     => admin_url( 'post.php?post=' . $course->ID . '&action=edit' ),
		);
	}
	
	return $recent_courses;
}
?>
