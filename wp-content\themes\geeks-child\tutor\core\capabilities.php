<?php
/**
 * Tutor LMS Course Automation - User Capabilities Management
 *
 * This file handles all user capability and permission management for the
 * course automation system.
 *
 * @package TutorLMS_CourseAutomation
 * @subpackage Core
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

/**
 * Adds the custom capability 'export_course_data' to the 'gestor' role.
 *
 * This function runs once when WordPress loads. It ensures the permission exists
 * so we can check for it later. This is the correct, standard WordPress way.
 */
function panapana_add_custom_export_capability() {
	// Get the 'gestor' role object.
	$role = get_role( 'gestor' );

	// IMPORTANT: Check if the role actually exists before trying to modify it.
	if ( ! empty( $role ) ) {
		// Add our custom capability to the role.
		// WordPress is smart and will only add it once.
		$role->add_cap( 'export_course_data', true );
	}

	// Also add capability to administrator role for easier testing
	$admin_role = get_role( 'administrator' );
	if ( ! empty( $admin_role ) ) {
		$admin_role->add_cap( 'export_course_data', true );
	}
}
add_action( 'init', 'panapana_add_custom_export_capability' );

/**
 * Checks if current user has permission to access course automation features.
 *
 * @return bool True if user has permission, false otherwise.
 */
function panapana_user_can_access_automation() {
	// Allow administrators and users with export_course_data capability
	if ( current_user_can( 'manage_options' ) || current_user_can( 'export_course_data' ) ) {
		return true;
	}

	// Temporary: Also allow any logged-in user for testing (remove this later)
	// return is_user_logged_in();

	return false;
}

/**
 * Returns permission denied message for unauthorized users.
 *
 * @return string HTML error message.
 */
function panapana_get_permission_denied_message() {
	return '<p>Você não tem permissão para acessar esta funcionalidade.</p>';
}

/**
 * Checks permissions and returns error message if user lacks access.
 * Used as a helper for shortcodes and functions.
 *
 * @return string|false Error message if no permission, false if user has access.
 */
function panapana_check_user_permissions() {
	if ( ! panapana_user_can_access_automation() ) {
		return panapana_get_permission_denied_message();
	}
	return false;
}

/**
 * Debug function to check current user permissions.
 * Add ?panapana_debug_permissions=1 to any admin page to see debug info.
 */
function panapana_debug_permissions() {
	if ( ! isset( $_GET['panapana_debug_permissions'] ) || ! current_user_can( 'manage_options' ) ) {
		return;
	}

	$current_user = wp_get_current_user();
	echo '<div style="background: #fff; border: 2px solid #0073aa; padding: 20px; margin: 20px; font-family: monospace;">';
	echo '<h3>🔍 Panapana Permissions Debug</h3>';
	echo '<p><strong>Current User:</strong> ' . $current_user->user_login . ' (ID: ' . $current_user->ID . ')</p>';
	echo '<p><strong>User Roles:</strong> ' . implode( ', ', $current_user->roles ) . '</p>';
	echo '<p><strong>Has export_course_data:</strong> ' . ( current_user_can( 'export_course_data' ) ? '✅ YES' : '❌ NO' ) . '</p>';
	echo '<p><strong>Has manage_options:</strong> ' . ( current_user_can( 'manage_options' ) ? '✅ YES' : '❌ NO' ) . '</p>';
	echo '<p><strong>Can access automation:</strong> ' . ( panapana_user_can_access_automation() ? '✅ YES' : '❌ NO' ) . '</p>';

	// Check if gestor role exists
	$gestor_role = get_role( 'gestor' );
	echo '<p><strong>Gestor role exists:</strong> ' . ( $gestor_role ? '✅ YES' : '❌ NO' ) . '</p>';
	if ( $gestor_role ) {
		echo '<p><strong>Gestor capabilities:</strong> ' . implode( ', ', array_keys( $gestor_role->capabilities ) ) . '</p>';
	}

	echo '</div>';
}
add_action( 'admin_notices', 'panapana_debug_permissions' );
