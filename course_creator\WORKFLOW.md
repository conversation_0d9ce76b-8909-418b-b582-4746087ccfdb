# 🔄 Development Workflow Guide

Complete guide for working with the Course Creator across multiple devices.

## 🏗️ **Initial Setup (New PC/Environment)**

### **Step 1: Clone Repository**
```bash
git clone https://github.com/dbs-web/course_creator.git
cd course_creator
```

### **Step 2: Run Setup Script**
```powershell
# Windows PowerShell
.\scripts\setup-new-environment.ps1

# If WordPress is in different location:
.\scripts\setup-new-environment.ps1 -WordPressPath "C:\xampp\htdocs\mysite"
```

### **Step 3: Manual WordPress Configuration**
1. **WordPress Admin → Appearance → Themes**
   - Activate "Geeks Child" theme
2. **WordPress Admin → Plugins**
   - Ensure "Tutor LMS" is active
3. **Test Installation**
   - Create page with `[panapana_master_debug]`
   - Verify all functions show "EXISTS"

---

## 💻 **Daily Development Workflow**

### **🎯 Recommended: Work in Repository First**

#### **Making Changes:**
```bash
# 1. Edit files in repository
code themes/geeks-child/tutor/core/course-creator.php

# 2. Deploy to WordPress
.\scripts\deploy-theme.ps1

# 3. Test in WordPress
# Visit your test page, upload CSV, etc.

# 4. Commit changes
git add .
git commit -m "Added new course creation feature"
git push origin main
```

#### **Pulling Updates from Other PC:**
```bash
# 1. Pull latest changes
git pull origin main

# 2. Deploy updated theme
.\scripts\deploy-theme.ps1

# 3. Test functionality
```

### **🔄 Alternative: Work in WordPress, Sync Back**

#### **Making Changes:**
```bash
# 1. Edit files in WordPress
code public/wp-content/themes/geeks-child/tutor/core/course-creator.php

# 2. Test in WordPress
# Upload CSV, test functionality

# 3. Sync changes back to repository
.\scripts\sync-from-wordpress.ps1

# 4. Review and commit
git diff
git add .
git commit -m "Updated course creation logic"
git push origin main
```

---

## 📁 **File Locations Explained**

### **Repository Structure:**
```
course_creator/
├── themes/geeks-child/          # 📝 EDIT HERE (recommended)
│   ├── functions.php
│   ├── style.css
│   └── tutor/
│       ├── core/
│       └── shortcodes/
├── scripts/                     # 🛠️ Automation scripts
├── backlog/sample-course.csv    # 🧪 Test data
└── docs/                        # 📚 Documentation
```

### **WordPress Structure:**
```
public/
└── wp-content/themes/geeks-child/  # 🚀 DEPLOYED HERE
    ├── functions.php               # (copy of repository)
    ├── style.css                   # (copy of repository)
    └── tutor/                      # (copy of repository)
```

---

## 🔧 **Script Reference**

### **deploy-theme.ps1**
Copies theme from repository to WordPress
```powershell
.\scripts\deploy-theme.ps1                    # Default: public/
.\scripts\deploy-theme.ps1 -WordPressPath "C:\xampp\htdocs"
```

### **sync-from-wordpress.ps1**
Copies changes from WordPress back to repository
```powershell
.\scripts\sync-from-wordpress.ps1             # Default: public/
.\scripts\sync-from-wordpress.ps1 -WordPressPath "C:\xampp\htdocs"
```

### **setup-new-environment.ps1**
Complete setup for new environment
```powershell
.\scripts\setup-new-environment.ps1           # Deploy + verify setup
```

---

## 🚨 **Common Scenarios**

### **Scenario 1: Work PC → Home PC**
```bash
# Work PC
git add .
git commit -m "Added quiz validation"
git push origin main

# Home PC
git pull origin main
.\scripts\deploy-theme.ps1
# Test functionality
```

### **Scenario 2: Emergency Fix in WordPress**
```bash
# Edit directly in WordPress
code public/wp-content/themes/geeks-child/tutor/core/quiz-creator.php

# Sync back to repository
.\scripts\sync-from-wordpress.ps1
git add .
git commit -m "Fixed quiz creation bug"
git push origin main
```

### **Scenario 3: Database Sync Needed**
```bash
# Export database (Work PC)
mysqldump -u user -p database > database/exports/courses_$(date +%Y%m%d).sql

# Import database (Home PC)
mysql -u user -p database < database/exports/courses_20250704.sql
```

---

## ✅ **Best Practices**

### **DO:**
- ✅ Always test after deploying
- ✅ Use descriptive commit messages
- ✅ Pull before making changes
- ✅ Use scripts for deployment
- ✅ Backup database before major changes

### **DON'T:**
- ❌ Edit both locations simultaneously
- ❌ Forget to deploy after repository changes
- ❌ Commit without testing
- ❌ Push broken code
- ❌ Skip database backups

---

## 🆘 **Troubleshooting**

### **Theme Not Working After Deploy:**
```bash
# Check file permissions
# Verify WordPress can read theme files
# Check PHP error logs
```

### **Changes Not Appearing:**
```bash
# Clear WordPress cache
# Check if correct theme is active
# Verify file deployment with:
ls -la public/wp-content/themes/geeks-child/tutor/
```

### **Git Conflicts:**
```bash
# Pull latest changes first
git pull origin main

# If conflicts, resolve manually then:
git add .
git commit -m "Resolved merge conflicts"
git push origin main
```

---

## 📞 **Quick Commands Cheat Sheet**

```bash
# Setup new environment
.\scripts\setup-new-environment.ps1

# Deploy theme changes
.\scripts\deploy-theme.ps1

# Sync WordPress changes back
.\scripts\sync-from-wordpress.ps1

# Standard Git workflow
git pull origin main
git add .
git commit -m "Description"
git push origin main
```

**🎯 This workflow ensures consistency across all your development environments!**
