<?php
/**
 * Panapana Course Automation - System Status Page
 *
 * System diagnostics and health check interface.
 *
 * @package TutorLMS_CourseAutomation
 * @subpackage Admin
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

// Security check
if ( ! panapana_user_can_access_automation() ) {
	wp_die( __( 'You do not have sufficient permissions to access this page.' ) );
}

// Get comprehensive system information
$system_info = panapana_get_comprehensive_system_info();

?>
<div class="panapana-admin-wrap">
	<h1><?php _e( 'System Status & Diagnostics', 'panapana' ); ?></h1>

	<div class="panapana-action-buttons" style="margin-bottom: 20px;">
		<button type="button" id="refresh-system-status" class="button">
			<span class="dashicons dashicons-update"></span>
			<?php _e( 'Refresh Status', 'panapana' ); ?>
		</button>
		<button type="button" onclick="PanapanaAdmin.exportSystemInfo()" class="button">
			<span class="dashicons dashicons-download"></span>
			<?php _e( 'Export System Info', 'panapana' ); ?>
		</button>
	</div>

	<div class="panapana-system-info">
		<!-- WordPress & Environment -->
		<div class="panapana-info-section">
			<h3><?php _e( 'WordPress Environment', 'panapana' ); ?></h3>
			<ul class="panapana-info-list">
				<li>
					<span class="panapana-info-label"><?php _e( 'WordPress Version', 'panapana' ); ?></span>
					<span class="panapana-info-value">
						<span class="panapana-status-indicator <?php echo version_compare( $system_info['wordpress']['version'], '5.0', '>=' ) ? 'success' : 'warning'; ?>"></span>
						<?php echo esc_html( $system_info['wordpress']['version'] ); ?>
					</span>
				</li>
				<li>
					<span class="panapana-info-label"><?php _e( 'PHP Version', 'panapana' ); ?></span>
					<span class="panapana-info-value">
						<span class="panapana-status-indicator <?php echo version_compare( $system_info['php']['version'], '7.4', '>=' ) ? 'success' : 'warning'; ?>"></span>
						<?php echo esc_html( $system_info['php']['version'] ); ?>
					</span>
				</li>
				<li>
					<span class="panapana-info-label"><?php _e( 'MySQL Version', 'panapana' ); ?></span>
					<span class="panapana-info-value">
						<span class="panapana-status-indicator <?php echo version_compare( $system_info['mysql']['version'], '5.6', '>=' ) ? 'success' : 'warning'; ?>"></span>
						<?php echo esc_html( $system_info['mysql']['version'] ); ?>
					</span>
				</li>
				<li>
					<span class="panapana-info-label"><?php _e( 'Memory Limit', 'panapana' ); ?></span>
					<span class="panapana-info-value">
						<span class="panapana-status-indicator <?php echo $system_info['php']['memory_limit_mb'] >= 256 ? 'success' : 'warning'; ?>"></span>
						<?php echo esc_html( $system_info['php']['memory_limit'] ); ?>
					</span>
				</li>
				<li>
					<span class="panapana-info-label"><?php _e( 'Max Upload Size', 'panapana' ); ?></span>
					<span class="panapana-info-value">
						<span class="panapana-status-indicator <?php echo $system_info['php']['upload_max_filesize_mb'] >= 10 ? 'success' : 'warning'; ?>"></span>
						<?php echo esc_html( $system_info['php']['upload_max_filesize'] ); ?>
					</span>
				</li>
			</ul>
		</div>

		<!-- Tutor LMS Status -->
		<div class="panapana-info-section">
			<h3><?php _e( 'Tutor LMS Status', 'panapana' ); ?></h3>
			<ul class="panapana-info-list">
				<li>
					<span class="panapana-info-label"><?php _e( 'Plugin Status', 'panapana' ); ?></span>
					<span class="panapana-info-value">
						<span class="panapana-status-indicator <?php echo $system_info['tutor']['active'] ? 'success' : 'error'; ?>"></span>
						<?php echo $system_info['tutor']['active'] ? __( 'Active', 'panapana' ) : __( 'Inactive', 'panapana' ); ?>
					</span>
				</li>
				<?php if ( $system_info['tutor']['active'] ) : ?>
					<li>
						<span class="panapana-info-label"><?php _e( 'Version', 'panapana' ); ?></span>
						<span class="panapana-info-value"><?php echo esc_html( $system_info['tutor']['version'] ); ?></span>
					</li>
					<li>
						<span class="panapana-info-label"><?php _e( 'Database Tables', 'panapana' ); ?></span>
						<span class="panapana-info-value">
							<span class="panapana-status-indicator <?php echo $system_info['tutor']['tables_exist'] ? 'success' : 'error'; ?>"></span>
							<?php echo $system_info['tutor']['tables_exist'] ? __( 'All Present', 'panapana' ) : __( 'Missing Tables', 'panapana' ); ?>
						</span>
					</li>
				<?php endif; ?>
			</ul>
		</div>

		<!-- Automation System -->
		<div class="panapana-info-section">
			<h3><?php _e( 'Course Automation', 'panapana' ); ?></h3>
			<ul class="panapana-info-list">
				<li>
					<span class="panapana-info-label"><?php _e( 'System Version', 'panapana' ); ?></span>
					<span class="panapana-info-value">
						<span class="panapana-status-indicator success"></span>
						v<?php echo esc_html( $system_info['automation']['version'] ); ?>
					</span>
				</li>
				<li>
					<span class="panapana-info-label"><?php _e( 'Core Functions', 'panapana' ); ?></span>
					<span class="panapana-info-value">
						<span class="panapana-status-indicator <?php echo $system_info['automation']['functions_available'] ? 'success' : 'error'; ?>"></span>
						<?php echo $system_info['automation']['functions_available'] ? __( 'Available', 'panapana' ) : __( 'Missing', 'panapana' ); ?>
					</span>
				</li>
				<li>
					<span class="panapana-info-label"><?php _e( 'User Permissions', 'panapana' ); ?></span>
					<span class="panapana-info-value">
						<span class="panapana-status-indicator <?php echo $system_info['automation']['user_can_access'] ? 'success' : 'warning'; ?>"></span>
						<?php echo $system_info['automation']['user_can_access'] ? __( 'Authorized', 'panapana' ) : __( 'Limited Access', 'panapana' ); ?>
					</span>
				</li>
				<li>
					<span class="panapana-info-label"><?php _e( 'Shortcodes', 'panapana' ); ?></span>
					<span class="panapana-info-value">
						<span class="panapana-status-indicator <?php echo $system_info['automation']['shortcodes_registered'] ? 'success' : 'warning'; ?>"></span>
						<?php echo $system_info['automation']['shortcodes_registered'] ? __( 'Registered', 'panapana' ) : __( 'Not Registered', 'panapana' ); ?>
					</span>
				</li>
			</ul>
		</div>

		<!-- File System -->
		<div class="panapana-info-section">
			<h3><?php _e( 'File System', 'panapana' ); ?></h3>
			<ul class="panapana-info-list">
				<li>
					<span class="panapana-info-label"><?php _e( 'Upload Directory', 'panapana' ); ?></span>
					<span class="panapana-info-value">
						<span class="panapana-status-indicator <?php echo $system_info['filesystem']['upload_dir_writable'] ? 'success' : 'error'; ?>"></span>
						<?php echo $system_info['filesystem']['upload_dir_writable'] ? __( 'Writable', 'panapana' ) : __( 'Not Writable', 'panapana' ); ?>
					</span>
				</li>
				<li>
					<span class="panapana-info-label"><?php _e( 'VTT Directory', 'panapana' ); ?></span>
					<span class="panapana-info-value">
						<span class="panapana-status-indicator <?php echo $system_info['filesystem']['vtt_dir_exists'] ? 'success' : 'warning'; ?>"></span>
						<?php echo $system_info['filesystem']['vtt_dir_exists'] ? __( 'Exists', 'panapana' ) : __( 'Not Found', 'panapana' ); ?>
					</span>
				</li>
				<li>
					<span class="panapana-info-label"><?php _e( 'Theme Directory', 'panapana' ); ?></span>
					<span class="panapana-info-value">
						<span class="panapana-status-indicator <?php echo $system_info['filesystem']['theme_dir_writable'] ? 'success' : 'warning'; ?>"></span>
						<?php echo $system_info['filesystem']['theme_dir_writable'] ? __( 'Writable', 'panapana' ) : __( 'Read Only', 'panapana' ); ?>
					</span>
				</li>
			</ul>
		</div>

		<!-- Statistics -->
		<div class="panapana-info-section">
			<h3><?php _e( 'Usage Statistics', 'panapana' ); ?></h3>
			<ul class="panapana-info-list">
				<li>
					<span class="panapana-info-label"><?php _e( 'Total Courses', 'panapana' ); ?></span>
					<span class="panapana-info-value"><?php echo esc_html( $system_info['statistics']['total_courses'] ); ?></span>
				</li>
				<li>
					<span class="panapana-info-label"><?php _e( 'Automated Courses', 'panapana' ); ?></span>
					<span class="panapana-info-value"><?php echo esc_html( $system_info['statistics']['automated_courses'] ); ?></span>
				</li>
				<li>
					<span class="panapana-info-label"><?php _e( 'Recent Uploads', 'panapana' ); ?></span>
					<span class="panapana-info-value"><?php echo esc_html( $system_info['statistics']['recent_uploads'] ); ?></span>
				</li>
				<li>
					<span class="panapana-info-label"><?php _e( 'Last Upload', 'panapana' ); ?></span>
					<span class="panapana-info-value"><?php echo esc_html( $system_info['statistics']['last_upload'] ); ?></span>
				</li>
			</ul>
		</div>

		<!-- Debug Information -->
		<div class="panapana-info-section">
			<h3><?php _e( 'Debug Information', 'panapana' ); ?></h3>
			<ul class="panapana-info-list">
				<li>
					<span class="panapana-info-label"><?php _e( 'WP Debug', 'panapana' ); ?></span>
					<span class="panapana-info-value">
						<span class="panapana-status-indicator <?php echo $system_info['debug']['wp_debug'] ? 'warning' : 'success'; ?>"></span>
						<?php echo $system_info['debug']['wp_debug'] ? __( 'Enabled', 'panapana' ) : __( 'Disabled', 'panapana' ); ?>
					</span>
				</li>
				<li>
					<span class="panapana-info-label"><?php _e( 'Error Log', 'panapana' ); ?></span>
					<span class="panapana-info-value">
						<span class="panapana-status-indicator <?php echo $system_info['debug']['error_log_exists'] ? 'warning' : 'success'; ?>"></span>
						<?php echo $system_info['debug']['error_log_exists'] ? __( 'Has Errors', 'panapana' ) : __( 'Clean', 'panapana' ); ?>
					</span>
				</li>
				<li>
					<span class="panapana-info-label"><?php _e( 'Current User', 'panapana' ); ?></span>
					<span class="panapana-info-value"><?php echo esc_html( $system_info['debug']['current_user'] ); ?></span>
				</li>
				<li>
					<span class="panapana-info-label"><?php _e( 'User Roles', 'panapana' ); ?></span>
					<span class="panapana-info-value"><?php echo esc_html( $system_info['debug']['user_roles'] ); ?></span>
				</li>
			</ul>
		</div>
	</div>

	<!-- Function Tests -->
	<div class="panapana-dashboard-card" style="margin-top: 20px;">
		<h2><?php _e( 'Function Tests', 'panapana' ); ?></h2>
		<p><?php _e( 'Test core automation functions to ensure they are working correctly.', 'panapana' ); ?></p>
		
		<div class="panapana-function-tests">
			<?php
			$function_tests = panapana_run_function_tests();
			foreach ( $function_tests as $test ) :
			?>
				<div class="panapana-test-result">
					<span class="panapana-status-indicator <?php echo $test['status']; ?>"></span>
					<strong><?php echo esc_html( $test['name'] ); ?></strong>
					<span class="panapana-test-message"><?php echo esc_html( $test['message'] ); ?></span>
				</div>
			<?php endforeach; ?>
		</div>
	</div>

	<!-- Troubleshooting -->
	<div class="panapana-dashboard-card" style="margin-top: 20px;">
		<h2><?php _e( 'Troubleshooting', 'panapana' ); ?></h2>
		<div class="panapana-troubleshooting">
			<h3><?php _e( 'Common Issues', 'panapana' ); ?></h3>
			<ul>
				<li><strong><?php _e( 'CSV upload fails:', 'panapana' ); ?></strong> <?php _e( 'Check file size and format. Ensure PHP upload limits are sufficient.', 'panapana' ); ?></li>
				<li><strong><?php _e( 'Courses not created:', 'panapana' ); ?></strong> <?php _e( 'Verify Tutor LMS is active and user has proper permissions.', 'panapana' ); ?></li>
				<li><strong><?php _e( 'Videos not displaying:', 'panapana' ); ?></strong> <?php _e( 'Check YouTube URLs and VTT file paths.', 'panapana' ); ?></li>
				<li><strong><?php _e( 'Permission denied:', 'panapana' ); ?></strong> <?php _e( 'Ensure user has "export_course_data" capability or is administrator.', 'panapana' ); ?></li>
			</ul>
		</div>
	</div>
</div>

<?php
/**
 * Get comprehensive system information
 */
function panapana_get_comprehensive_system_info() {
	global $wpdb;
	
	// Get upload directory info
	$upload_dir = wp_upload_dir();
	
	// Get VTT directory info
	$vtt_dir = $upload_dir['basedir'] . '/vtt';
	
	// Get theme directory info
	$theme_dir = get_stylesheet_directory() . '/tutor';
	
	// Get recent upload count
	$upload_history = get_option( 'panapana_upload_history', array() );
	$recent_uploads = count( array_filter( $upload_history, function( $upload ) {
		return strtotime( $upload['date'] ) > strtotime( '-30 days' );
	} ) );
	
	// Get last upload date
	$last_upload = ! empty( $upload_history ) ? $upload_history[0]['date'] : __( 'Never', 'panapana' );
	
	return array(
		'wordpress' => array(
			'version' => get_bloginfo( 'version' ),
		),
		'php' => array(
			'version' => PHP_VERSION,
			'memory_limit' => ini_get( 'memory_limit' ),
			'memory_limit_mb' => (int) ini_get( 'memory_limit' ),
			'upload_max_filesize' => ini_get( 'upload_max_filesize' ),
			'upload_max_filesize_mb' => (int) ini_get( 'upload_max_filesize' ),
		),
		'mysql' => array(
			'version' => $wpdb->db_version(),
		),
		'tutor' => array(
			'active' => class_exists( 'TUTOR' ),
			'version' => class_exists( 'TUTOR' ) ? ( defined( 'TUTOR_VERSION' ) ? TUTOR_VERSION : 'Unknown' ) : 'N/A',
			'tables_exist' => panapana_check_tutor_tables(),
		),
		'automation' => array(
			'version' => PANAPANA_AUTOMATION_VERSION,
			'functions_available' => panapana_check_core_functions(),
			'user_can_access' => panapana_user_can_access_automation(),
			'shortcodes_registered' => panapana_check_shortcodes_registered(),
		),
		'filesystem' => array(
			'upload_dir_writable' => is_writable( $upload_dir['basedir'] ),
			'vtt_dir_exists' => is_dir( $vtt_dir ),
			'theme_dir_writable' => is_writable( $theme_dir ),
		),
		'statistics' => array(
			'total_courses' => wp_count_posts( 'courses' )->publish,
			'automated_courses' => $wpdb->get_var( "SELECT COUNT(DISTINCT post_id) FROM {$wpdb->postmeta} WHERE meta_key = '_panapana_automated' AND meta_value = '1'" ),
			'recent_uploads' => $recent_uploads,
			'last_upload' => $last_upload,
		),
		'debug' => array(
			'wp_debug' => defined( 'WP_DEBUG' ) && WP_DEBUG,
			'error_log_exists' => file_exists( ini_get( 'error_log' ) ),
			'current_user' => wp_get_current_user()->display_name,
			'user_roles' => implode( ', ', wp_get_current_user()->roles ),
		),
	);
}

/**
 * Check if Tutor LMS tables exist
 */
function panapana_check_tutor_tables() {
	global $wpdb;
	
	$required_tables = array(
		$wpdb->prefix . 'tutor_quiz_questions',
		$wpdb->prefix . 'tutor_quiz_question_answers',
	);
	
	foreach ( $required_tables as $table ) {
		if ( $wpdb->get_var( "SHOW TABLES LIKE '{$table}'" ) !== $table ) {
			return false;
		}
	}
	
	return true;
}

/**
 * Check if core automation functions are available
 */
function panapana_check_core_functions() {
	$required_functions = array(
		'panapana_create_course_from_data',
		'panapana_read_csv_file',
		'panapana_group_csv_data_by_course',
		'panapana_export_course_data',
	);
	
	foreach ( $required_functions as $function ) {
		if ( ! function_exists( $function ) ) {
			return false;
		}
	}
	
	return true;
}

/**
 * Check if shortcodes are registered
 */
function panapana_check_shortcodes_registered() {
	global $shortcode_tags;
	
	$required_shortcodes = array(
		'tutor_course_exporter',
		'panapana_csv_course_creator',
		'panapana_hello_world_course',
		'panapana_master_debug',
	);
	
	foreach ( $required_shortcodes as $shortcode ) {
		if ( ! isset( $shortcode_tags[ $shortcode ] ) ) {
			return false;
		}
	}
	
	return true;
}

/**
 * Run function tests
 */
function panapana_run_function_tests() {
	$tests = array();
	
	// Test CSV parser
	$tests[] = array(
		'name' => __( 'CSV Parser', 'panapana' ),
		'status' => function_exists( 'panapana_read_csv_file' ) ? 'success' : 'error',
		'message' => function_exists( 'panapana_read_csv_file' ) ? __( 'Available', 'panapana' ) : __( 'Function not found', 'panapana' ),
	);
	
	// Test course creator
	$tests[] = array(
		'name' => __( 'Course Creator', 'panapana' ),
		'status' => function_exists( 'panapana_create_course_from_data' ) ? 'success' : 'error',
		'message' => function_exists( 'panapana_create_course_from_data' ) ? __( 'Available', 'panapana' ) : __( 'Function not found', 'panapana' ),
	);
	
	// Test course exporter
	$tests[] = array(
		'name' => __( 'Course Exporter', 'panapana' ),
		'status' => function_exists( 'panapana_export_course_data' ) ? 'success' : 'error',
		'message' => function_exists( 'panapana_export_course_data' ) ? __( 'Available', 'panapana' ) : __( 'Function not found', 'panapana' ),
	);
	
	// Test database connection
	global $wpdb;
	$db_test = $wpdb->get_var( "SELECT 1" );
	$tests[] = array(
		'name' => __( 'Database Connection', 'panapana' ),
		'status' => $db_test === '1' ? 'success' : 'error',
		'message' => $db_test === '1' ? __( 'Connected', 'panapana' ) : __( 'Connection failed', 'panapana' ),
	);
	
	return $tests;
}
?>
