row_type,course_title,topic_title,item_title,lesson_type,content,description,order,video_url,vtt_path,pdf_url,quiz_settings,question_type,question_points,answer_text,is_correct,answer_order
course,"Git - Iniciante ao Avançado","","","","","Aprenda Git do básico ao avançado com exemplos práticos e exercícios. Este curso aborda desde os conceitos fundamentais até técnicas avançadas de versionamento.",1,"","","","","","","","",""
topic,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","","","","Conceitos fundamentais do Git e configuração inicial do ambiente de desenvolvimento.",1,"","","","","","","","",""
lesson,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","O que é Git?",video,"","Introdução aos conceitos básicos do Git e sua importância no desenvolvimento de software.",1,"https://www.youtube.com/watch?v=4n4I0EqvBk0","4n4I0EqvBk0.pt.vtt","","","","","","",""
lesson,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Instalação e Configuração",video,"","Como instalar o Git em diferentes sistemas operacionais e configurar suas primeiras credenciais.",2,"https://www.youtube.com/watch?v=FLqi7on3vU4","FLqi7on3vU4.pt.vtt","","","","","","",""
lesson,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Manual Git - Comandos Básicos",ebook,"","Manual completo em PDF com os principais comandos Git para consulta rápida.",3,"","","https://cursos.institutopanapana.org.br/wp-content/uploads/2025/07/git-comandos-basicos.pdf","","","","","",""
quiz,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos do Git","","","Teste seus conhecimentos sobre os conceitos fundamentais do Git.",4,"","","","time_limit:15,passing_score:70,max_attempts:3","","","","",""
question,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos do Git","","O que é Git?","",1,"","","","",single_choice,1.00,"","",""
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos do Git","","","",1,"","","","","","","Um sistema de controle de versão distribuído",1,1
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos do Git","","","",1,"","","","","","","Um editor de texto avançado",0,2
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos do Git","","","",1,"","","","","","","Uma linguagem de programação",0,3
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos do Git","","","",1,"","","","","","","Um banco de dados",0,4
question,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos do Git","","Qual comando é usado para inicializar um repositório Git?","",2,"","","","",single_choice,1.00,"","",""
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos do Git","","","",2,"","","","","","","git init",1,1
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos do Git","","","",2,"","","","","","","git start",0,2
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos do Git","","","",2,"","","","","","","git create",0,3
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos do Git","","","",2,"","","","","","","git new",0,4
question,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos do Git","","O Git é um sistema centralizado?","",3,"","","","",true_false,1.00,"","",""
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos do Git","","","",3,"","","","","","","Verdadeiro",0,1
answer,"Git - Iniciante ao Avançado","Módulo 1: Introdução ao Git","Quiz: Fundamentos do Git","","","",3,"","","","","","","Falso",1,2
topic,"Git - Iniciante ao Avançado","Módulo 2: Comandos Básicos","","","","Aprenda os comandos essenciais para trabalhar com Git no dia a dia.",2,"","","","","","","","",""
lesson,"Git - Iniciante ao Avançado","Módulo 2: Comandos Básicos","Git Add e Commit",video,"","Como adicionar arquivos ao staging area e fazer commits eficientes.",1,"https://www.youtube.com/watch?v=example1","example1.pt.vtt","","","","","","",""
lesson,"Git - Iniciante ao Avançado","Módulo 2: Comandos Básicos","Git Status e Log",video,"","Verificando o status do repositório e visualizando o histórico de commits.",2,"https://www.youtube.com/watch?v=example2","example2.pt.vtt","","","","","","",""
lesson,"Git - Iniciante ao Avançado","Módulo 2: Comandos Básicos","Cheat Sheet - Comandos Essenciais",ebook,"","Guia de referência rápida com os comandos mais utilizados no Git.",3,"","","https://cursos.institutopanapana.org.br/wp-content/uploads/2025/07/git-cheat-sheet.pdf","","","","","",""
quiz,"Git - Iniciante ao Avançado","Módulo 2: Comandos Básicos","Quiz: Comandos Básicos","","","Teste seus conhecimentos sobre os comandos básicos do Git.",4,"","","","time_limit:20,passing_score:75,max_attempts:2","","","","",""
question,"Git - Iniciante ao Avançado","Módulo 2: Comandos Básicos","Quiz: Comandos Básicos","","Qual comando adiciona arquivos ao staging area?","",1,"","","","",single_choice,1.00,"","",""
answer,"Git - Iniciante ao Avançado","Módulo 2: Comandos Básicos","Quiz: Comandos Básicos","","","",1,"","","","","","","git add",1,1
answer,"Git - Iniciante ao Avançado","Módulo 2: Comandos Básicos","Quiz: Comandos Básicos","","","",1,"","","","","","","git stage",0,2
answer,"Git - Iniciante ao Avançado","Módulo 2: Comandos Básicos","Quiz: Comandos Básicos","","","",1,"","","","","","","git prepare",0,3
answer,"Git - Iniciante ao Avançado","Módulo 2: Comandos Básicos","Quiz: Comandos Básicos","","","",1,"","","","","","","git include",0,4
question,"Git - Iniciante ao Avançado","Módulo 2: Comandos Básicos","Quiz: Comandos Básicos","","O comando 'git status' mostra apenas arquivos modificados?","",2,"","","","",true_false,1.00,"","",""
answer,"Git - Iniciante ao Avançado","Módulo 2: Comandos Básicos","Quiz: Comandos Básicos","","","",2,"","","","","","","Verdadeiro",0,1
answer,"Git - Iniciante ao Avançado","Módulo 2: Comandos Básicos","Quiz: Comandos Básicos","","","",2,"","","","","","","Falso",1,2
