<?php
/**
 * Panapana Course Automation - Course Management Page
 *
 * Interface for managing automated courses.
 *
 * @package TutorLMS_CourseAutomation
 * @subpackage Admin
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

// Security check
if ( ! panapana_user_can_access_automation() ) {
	wp_die( __( 'You do not have sufficient permissions to access this page.' ) );
}

// Handle bulk actions
$bulk_action_result = null;
if ( isset( $_POST['bulk_action'] ) && wp_verify_nonce( $_POST['panapana_nonce'], 'panapana_bulk_action' ) ) {
	$bulk_action_result = panapana_handle_bulk_action();
}

// Get filter parameters
$filter_status = isset( $_GET['status'] ) ? sanitize_text_field( $_GET['status'] ) : 'all';
$filter_date = isset( $_GET['date'] ) ? sanitize_text_field( $_GET['date'] ) : 'all';
$search_term = isset( $_GET['s'] ) ? sanitize_text_field( $_GET['s'] ) : '';

// Get courses
$courses = panapana_get_automated_courses( $filter_status, $filter_date, $search_term );
$total_courses = count( $courses );

?>
<div class="panapana-admin-wrap">
	<h1><?php _e( 'Manage Automated Courses', 'panapana' ); ?></h1>

	<?php if ( $bulk_action_result ) : ?>
		<div class="panapana-notice <?php echo $bulk_action_result['success'] ? 'success' : 'error'; ?>">
			<p><?php echo esc_html( $bulk_action_result['message'] ); ?></p>
		</div>
	<?php endif; ?>

	<!-- Filters and Search -->
	<div class="panapana-dashboard-card">
		<form method="get" action="">
			<input type="hidden" name="page" value="panapana-course-management">
			<div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
				<div>
					<label for="status-filter"><?php _e( 'Status:', 'panapana' ); ?></label>
					<select name="status" id="status-filter">
						<option value="all" <?php selected( $filter_status, 'all' ); ?>><?php _e( 'All Statuses', 'panapana' ); ?></option>
						<option value="publish" <?php selected( $filter_status, 'publish' ); ?>><?php _e( 'Published', 'panapana' ); ?></option>
						<option value="draft" <?php selected( $filter_status, 'draft' ); ?>><?php _e( 'Draft', 'panapana' ); ?></option>
						<option value="private" <?php selected( $filter_status, 'private' ); ?>><?php _e( 'Private', 'panapana' ); ?></option>
					</select>
				</div>
				<div>
					<label for="date-filter"><?php _e( 'Date:', 'panapana' ); ?></label>
					<select name="date" id="date-filter">
						<option value="all" <?php selected( $filter_date, 'all' ); ?>><?php _e( 'All Dates', 'panapana' ); ?></option>
						<option value="today" <?php selected( $filter_date, 'today' ); ?>><?php _e( 'Today', 'panapana' ); ?></option>
						<option value="week" <?php selected( $filter_date, 'week' ); ?>><?php _e( 'This Week', 'panapana' ); ?></option>
						<option value="month" <?php selected( $filter_date, 'month' ); ?>><?php _e( 'This Month', 'panapana' ); ?></option>
					</select>
				</div>
				<div>
					<label for="search-courses"><?php _e( 'Search:', 'panapana' ); ?></label>
					<input type="text" name="s" id="search-courses" value="<?php echo esc_attr( $search_term ); ?>" placeholder="<?php _e( 'Search courses...', 'panapana' ); ?>">
				</div>
				<div>
					<button type="submit" class="button"><?php _e( 'Filter', 'panapana' ); ?></button>
					<a href="<?php echo admin_url( 'admin.php?page=panapana-course-management' ); ?>" class="button"><?php _e( 'Reset', 'panapana' ); ?></a>
				</div>
			</div>
		</form>
	</div>

	<!-- Course List -->
	<div class="panapana-dashboard-card">
		<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
			<h2><?php printf( __( 'Courses (%d)', 'panapana' ), $total_courses ); ?></h2>
			<div class="panapana-action-buttons">
				<a href="<?php echo admin_url( 'admin.php?page=panapana-csv-upload' ); ?>" class="panapana-action-button">
					<span class="dashicons dashicons-upload"></span>
					<?php _e( 'Upload New CSV', 'panapana' ); ?>
				</a>
				<button type="button" onclick="panapanaExportSelected()" class="panapana-action-button">
					<span class="dashicons dashicons-download"></span>
					<?php _e( 'Export Selected', 'panapana' ); ?>
				</button>
			</div>
		</div>

		<?php if ( ! empty( $courses ) ) : ?>
			<form method="post" id="bulk-action-form">
				<?php wp_nonce_field( 'panapana_bulk_action', 'panapana_nonce' ); ?>
				
				<!-- Bulk Actions -->
				<div class="tablenav top">
					<div class="alignleft actions">
						<select name="bulk_action">
							<option value=""><?php _e( 'Bulk Actions', 'panapana' ); ?></option>
							<option value="publish"><?php _e( 'Publish', 'panapana' ); ?></option>
							<option value="draft"><?php _e( 'Move to Draft', 'panapana' ); ?></option>
							<option value="delete"><?php _e( 'Delete', 'panapana' ); ?></option>
							<option value="export"><?php _e( 'Export Data', 'panapana' ); ?></option>
						</select>
						<button type="submit" class="button action"><?php _e( 'Apply', 'panapana' ); ?></button>
					</div>
				</div>

				<table class="panapana-course-table">
					<thead>
						<tr>
							<th style="width: 40px;">
								<input type="checkbox" id="select-all-courses">
							</th>
							<th><?php _e( 'Course', 'panapana' ); ?></th>
							<th><?php _e( 'Status', 'panapana' ); ?></th>
							<th><?php _e( 'Content', 'panapana' ); ?></th>
							<th><?php _e( 'Created', 'panapana' ); ?></th>
							<th><?php _e( 'Upload Info', 'panapana' ); ?></th>
							<th><?php _e( 'Actions', 'panapana' ); ?></th>
						</tr>
					</thead>
					<tbody>
						<?php foreach ( $courses as $course ) : ?>
							<tr>
								<td>
									<input type="checkbox" name="course_ids[]" value="<?php echo esc_attr( $course['id'] ); ?>" class="course-checkbox">
								</td>
								<td>
									<strong>
										<a href="<?php echo esc_url( $course['edit_url'] ); ?>">
											<?php echo esc_html( $course['title'] ); ?>
										</a>
									</strong>
									<div class="course-meta">
										<small><?php printf( __( 'ID: %d', 'panapana' ), $course['id'] ); ?></small>
									</div>
								</td>
								<td>
									<span class="panapana-status-indicator <?php echo esc_attr( $course['status_class'] ); ?>"></span>
									<?php echo esc_html( $course['status_text'] ); ?>
								</td>
								<td>
									<div class="course-content-summary">
										<div><?php printf( __( '%d topics', 'panapana' ), $course['topics_count'] ); ?></div>
										<div><?php printf( __( '%d lessons', 'panapana' ), $course['lessons_count'] ); ?></div>
										<div><?php printf( __( '%d quizzes', 'panapana' ), $course['quizzes_count'] ); ?></div>
									</div>
								</td>
								<td>
									<div><?php echo esc_html( $course['created_date'] ); ?></div>
									<small><?php echo esc_html( $course['created_time'] ); ?></small>
								</td>
								<td>
									<div><strong><?php _e( 'File:', 'panapana' ); ?></strong> <?php echo esc_html( $course['upload_filename'] ); ?></div>
									<div><strong><?php _e( 'Date:', 'panapana' ); ?></strong> <?php echo esc_html( $course['upload_date'] ); ?></div>
									<div><strong><?php _e( 'User:', 'panapana' ); ?></strong> <?php echo esc_html( $course['upload_user'] ); ?></div>
								</td>
								<td>
									<div class="course-actions">
										<a href="<?php echo esc_url( $course['edit_url'] ); ?>" class="button button-small">
											<?php _e( 'Edit', 'panapana' ); ?>
										</a>
										<a href="<?php echo esc_url( $course['view_url'] ); ?>" class="button button-small" target="_blank">
											<?php _e( 'View', 'panapana' ); ?>
										</a>
										<a href="<?php echo esc_url( $course['export_url'] ); ?>" class="button button-small">
											<?php _e( 'Export', 'panapana' ); ?>
										</a>
									</div>
								</td>
							</tr>
						<?php endforeach; ?>
					</tbody>
				</table>
			</form>
		<?php else : ?>
			<div class="panapana-empty-state">
				<span class="dashicons dashicons-welcome-learn-more" style="font-size: 48px; color: #c3c4c7; margin-bottom: 15px;"></span>
				<h3><?php _e( 'No automated courses found', 'panapana' ); ?></h3>
				<p><?php _e( 'Start by uploading a CSV file to create your first automated course.', 'panapana' ); ?></p>
				<a href="<?php echo admin_url( 'admin.php?page=panapana-csv-upload' ); ?>" class="panapana-action-button">
					<span class="dashicons dashicons-upload"></span>
					<?php _e( 'Upload CSV', 'panapana' ); ?>
				</a>
			</div>
		<?php endif; ?>
	</div>
</div>

<script>
jQuery(document).ready(function($) {
	// Select all checkbox functionality
	$('#select-all-courses').on('change', function() {
		$('.course-checkbox').prop('checked', this.checked);
	});
	
	// Update select all when individual checkboxes change
	$('.course-checkbox').on('change', function() {
		var total = $('.course-checkbox').length;
		var checked = $('.course-checkbox:checked').length;
		$('#select-all-courses').prop('checked', total === checked);
	});
});

function panapanaExportSelected() {
	var selected = [];
	jQuery('.course-checkbox:checked').each(function() {
		selected.push(jQuery(this).val());
	});
	
	if (selected.length === 0) {
		alert('<?php _e( 'Please select at least one course to export.', 'panapana' ); ?>');
		return;
	}
	
	var url = '<?php echo admin_url( 'admin-ajax.php' ); ?>?action=panapana_export_courses&nonce=<?php echo wp_create_nonce( 'panapana_export_courses' ); ?>&course_ids=' + selected.join(',');
	window.location.href = url;
}
</script>

<?php
/**
 * Get automated courses with filters
 */
function panapana_get_automated_courses( $status = 'all', $date = 'all', $search = '' ) {
	global $wpdb;
	
	// Build query
	$where_clauses = array( "pm.meta_key = '_panapana_automated' AND pm.meta_value = '1'" );
	
	// Status filter
	if ( $status !== 'all' ) {
		$where_clauses[] = $wpdb->prepare( "p.post_status = %s", $status );
	}
	
	// Date filter
	if ( $date !== 'all' ) {
		switch ( $date ) {
			case 'today':
				$where_clauses[] = "DATE(p.post_date) = CURDATE()";
				break;
			case 'week':
				$where_clauses[] = "p.post_date >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
				break;
			case 'month':
				$where_clauses[] = "p.post_date >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
				break;
		}
	}
	
	// Search filter
	if ( ! empty( $search ) ) {
		$where_clauses[] = $wpdb->prepare( "p.post_title LIKE %s", '%' . $wpdb->esc_like( $search ) . '%' );
	}
	
	$where_sql = implode( ' AND ', $where_clauses );
	
	$courses = $wpdb->get_results( "
		SELECT p.ID, p.post_title, p.post_date, p.post_status, p.post_author
		FROM {$wpdb->posts} p
		INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
		WHERE p.post_type = 'courses' AND {$where_sql}
		ORDER BY p.post_date DESC
	" );
	
	$formatted_courses = array();
	foreach ( $courses as $course ) {
		// Get course metadata
		$upload_filename = get_post_meta( $course->ID, '_panapana_upload_filename', true );
		$upload_date = get_post_meta( $course->ID, '_panapana_upload_date', true );
		$upload_user = get_userdata( $course->post_author );
		
		// Get content counts
		$topics_count = $wpdb->get_var( $wpdb->prepare( "
			SELECT COUNT(*) FROM {$wpdb->posts} 
			WHERE post_type = 'topics' AND post_parent = %d AND post_status = 'publish'
		", $course->ID ) );
		
		$lessons_count = $wpdb->get_var( $wpdb->prepare( "
			SELECT COUNT(*) FROM {$wpdb->posts} 
			WHERE post_type = 'lesson' AND post_parent = %d AND post_status = 'publish'
		", $course->ID ) );
		
		$quizzes_count = $wpdb->get_var( $wpdb->prepare( "
			SELECT COUNT(*) FROM {$wpdb->posts} 
			WHERE post_type = 'tutor_quiz' AND post_parent = %d AND post_status = 'publish'
		", $course->ID ) );
		
		$formatted_courses[] = array(
			'id' => $course->ID,
			'title' => $course->post_title,
			'status_class' => $course->post_status === 'publish' ? 'success' : ( $course->post_status === 'draft' ? 'warning' : 'error' ),
			'status_text' => ucfirst( $course->post_status ),
			'topics_count' => (int) $topics_count,
			'lessons_count' => (int) $lessons_count,
			'quizzes_count' => (int) $quizzes_count,
			'created_date' => mysql2date( 'M j, Y', $course->post_date ),
			'created_time' => mysql2date( 'g:i A', $course->post_date ),
			'upload_filename' => $upload_filename ?: __( 'Unknown', 'panapana' ),
			'upload_date' => $upload_date ? mysql2date( 'M j, Y', $upload_date ) : __( 'Unknown', 'panapana' ),
			'upload_user' => $upload_user ? $upload_user->display_name : __( 'Unknown', 'panapana' ),
			'edit_url' => admin_url( 'post.php?post=' . $course->ID . '&action=edit' ),
			'view_url' => get_permalink( $course->ID ),
			'export_url' => admin_url( 'admin-ajax.php?action=panapana_export_single_course&course_id=' . $course->ID . '&nonce=' . wp_create_nonce( 'panapana_export_single' ) ),
		);
	}
	
	return $formatted_courses;
}

/**
 * Handle bulk actions
 */
function panapana_handle_bulk_action() {
	$action = sanitize_text_field( $_POST['bulk_action'] );
	$course_ids = array_map( 'intval', $_POST['course_ids'] ?? array() );
	
	if ( empty( $action ) || empty( $course_ids ) ) {
		return array(
			'success' => false,
			'message' => __( 'Please select an action and at least one course.', 'panapana' ),
		);
	}
	
	$processed = 0;
	
	switch ( $action ) {
		case 'publish':
		case 'draft':
			foreach ( $course_ids as $course_id ) {
				if ( wp_update_post( array( 'ID' => $course_id, 'post_status' => $action ) ) ) {
					$processed++;
				}
			}
			$message = sprintf( 
				_n( 
					'%d course status updated.', 
					'%d courses status updated.', 
					$processed, 
					'panapana' 
				), 
				$processed 
			);
			break;
			
		case 'delete':
			foreach ( $course_ids as $course_id ) {
				if ( wp_delete_post( $course_id, true ) ) {
					$processed++;
				}
			}
			$message = sprintf( 
				_n( 
					'%d course deleted.', 
					'%d courses deleted.', 
					$processed, 
					'panapana' 
				), 
				$processed 
			);
			break;
			
		case 'export':
			// Redirect to export with selected course IDs
			$export_url = admin_url( 'admin-ajax.php?action=panapana_export_courses&course_ids=' . implode( ',', $course_ids ) . '&nonce=' . wp_create_nonce( 'panapana_export_courses' ) );
			wp_redirect( $export_url );
			exit;
			
		default:
			return array(
				'success' => false,
				'message' => __( 'Invalid action selected.', 'panapana' ),
			);
	}
	
	return array(
		'success' => true,
		'message' => $message,
	);
}
?>
