/**
 * Panapana Course Automation - Admin Styles
 * 
 * CSS styles for the WordPress admin interface of the course automation system.
 */

/* Main admin wrapper */
.panapana-admin-wrap {
    margin: 20px 20px 0 2px;
    max-width: 1200px;
}

.panapana-admin-wrap h1 {
    margin-bottom: 20px;
    font-size: 23px;
    font-weight: 400;
    line-height: 1.3;
}

/* Dashboard styles */
.panapana-dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.panapana-dashboard-card {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.panapana-dashboard-card h2 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
}

.panapana-dashboard-card h3 {
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 600;
}

/* Status indicators */
.panapana-status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.panapana-status-indicator.success {
    background-color: #46b450;
}

.panapana-status-indicator.warning {
    background-color: #ffb900;
}

.panapana-status-indicator.error {
    background-color: #dc3232;
}

/* Quick stats */
.panapana-quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.panapana-stat-item {
    text-align: center;
    padding: 15px;
    background: #f6f7f7;
    border-radius: 4px;
}

.panapana-stat-number {
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: #1d2327;
    margin-bottom: 5px;
}

.panapana-stat-label {
    font-size: 13px;
    color: #646970;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Action buttons */
.panapana-action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 20px;
}

.panapana-action-button {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    background: #2271b1;
    color: #fff;
    text-decoration: none;
    border-radius: 3px;
    font-size: 13px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.panapana-action-button:hover {
    background: #135e96;
    color: #fff;
}

.panapana-action-button .dashicons {
    margin-right: 5px;
    font-size: 16px;
}

/* CSV Upload styles */
.panapana-upload-area {
    border: 2px dashed #c3c4c7;
    border-radius: 4px;
    padding: 40px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.panapana-upload-area:hover,
.panapana-upload-area.dragover {
    border-color: #2271b1;
    background: #f0f6fc;
}

.panapana-upload-area .dashicons {
    font-size: 48px;
    color: #c3c4c7;
    margin-bottom: 15px;
}

.panapana-upload-text {
    font-size: 16px;
    color: #50575e;
    margin-bottom: 10px;
}

.panapana-upload-hint {
    font-size: 13px;
    color: #646970;
}

/* Progress bar */
.panapana-progress-container {
    margin: 20px 0;
    display: none;
}

.panapana-progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f1;
    border-radius: 10px;
    overflow: hidden;
}

.panapana-progress-fill {
    height: 100%;
    background: #2271b1;
    width: 0%;
    transition: width 0.3s ease;
}

.panapana-progress-text {
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
    color: #50575e;
}

/* Course list table */
.panapana-course-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.panapana-course-table th,
.panapana-course-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #c3c4c7;
}

.panapana-course-table th {
    background: #f6f7f7;
    font-weight: 600;
    font-size: 13px;
    color: #1d2327;
}

.panapana-course-table tr:hover {
    background: #f6f7f7;
}

/* System status styles */
.panapana-system-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.panapana-info-section {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
}

.panapana-info-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

.panapana-info-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.panapana-info-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f1;
}

.panapana-info-list li:last-child {
    border-bottom: none;
}

.panapana-info-label {
    font-weight: 500;
    color: #1d2327;
}

.panapana-info-value {
    color: #646970;
}

/* Notices */
.panapana-notice {
    margin: 20px 0;
    padding: 12px;
    border-left: 4px solid;
    background: #fff;
}

.panapana-notice.success {
    border-left-color: #46b450;
    background: #ecf7ed;
}

.panapana-notice.warning {
    border-left-color: #ffb900;
    background: #fff8e5;
}

.panapana-notice.error {
    border-left-color: #dc3232;
    background: #fcf0f1;
}

/* Responsive design */
@media (max-width: 768px) {
    .panapana-dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .panapana-quick-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .panapana-action-buttons {
        flex-direction: column;
    }
    
    .panapana-system-info {
        grid-template-columns: 1fr;
    }
}
