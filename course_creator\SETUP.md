# 🛠️ Course Creator Setup Guide

This guide will help you set up the Course Creator system on a new environment (work PC, home PC, or server).

## 📋 Prerequisites

### Required Software:
- **WordPress** 5.0 or higher
- **PHP** 7.4 or higher  
- **MySQL** 5.7 or higher
- **Git** for version control

### Required WordPress Plugins:
- **Tutor LMS** (latest version)
- **Geeks Theme** (or compatible theme)

### Required WordPress Capabilities:
- Admin access to install themes and plugins
- Ability to create/edit pages
- File system access for theme installation

## 🚀 Quick Setup (New Environment)

### 1. Clone Repository
```bash
git clone https://github.com/dbs-web/course_creator.git
cd course_creator
```

### 2. Install WordPress Dependencies
```bash
# Install Tutor LMS plugin
# Download from: https://wordpress.org/plugins/tutor/

# Install Geeks theme
# Purchase and download from theme provider
```

### 3. Deploy Child Theme
```bash
# Copy child theme to WordPress installation
cp -r themes/geeks-child/ /path/to/wordpress/wp-content/themes/

# Or on Windows:
xcopy themes\geeks-child\ C:\path\to\wordpress\wp-content\themes\geeks-child\ /E /I
```

### 4. Activate Theme
1. Go to WordPress Admin → Appearance → Themes
2. Activate "Geeks Child" theme
3. Verify the automation system loads without errors

### 5. Test Installation
1. Create a test page
2. Add shortcode: `[panapana_master_debug]`
3. Verify all functions show as "EXISTS"
4. Test CSV upload with `backlog/sample-course.csv`

## 🔄 Multi-Device Workflow

### Setting Up Second Environment

#### On Work PC (Primary):
```bash
# Make changes to the system
git add .
git commit -m "Description of changes"
git push origin main
```

#### On Home PC (Secondary):
```bash
# First time setup
git clone https://github.com/dbs-web/course_creator.git
# Follow steps 2-5 above

# Subsequent updates
git pull origin main
# Sync database if needed (see Database Sync section)
```

## 🗄️ Database Synchronization

### Export Database (Work PC):
```bash
# Export full database
mysqldump -u username -p database_name > database/exports/full_backup_YYYY-MM-DD.sql

# Export only course-related tables
mysqldump -u username -p database_name wp_posts wp_postmeta wp_tutor_quiz_questions wp_tutor_quiz_question_answers > database/exports/courses_only_YYYY-MM-DD.sql
```

### Import Database (Home PC):
```bash
# Import full database (careful - overwrites everything)
mysql -u username -p database_name < database/exports/full_backup_YYYY-MM-DD.sql

# Import only course data (safer)
mysql -u username -p database_name < database/exports/courses_only_YYYY-MM-DD.sql
```

### Alternative: WordPress Export/Import
1. **Export**: WordPress Admin → Tools → Export → All content
2. **Import**: WordPress Admin → Tools → Import → WordPress

## ⚙️ Configuration

### 1. User Permissions
The system automatically configures permissions, but verify:
- `gestor` role users can access course automation
- Administrators have full access
- Other roles are properly restricted

### 2. File Permissions
Ensure WordPress can write to:
- `/wp-content/uploads/` (for file uploads)
- Theme directory (for any dynamic files)

### 3. PHP Configuration
Recommended PHP settings:
```ini
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 300
memory_limit = 256M
```

## 🧪 Verification Checklist

### ✅ Basic Functionality:
- [ ] WordPress loads without errors
- [ ] Tutor LMS is active and configured
- [ ] Child theme is active
- [ ] Shortcodes render properly (not as plain text)

### ✅ Course Automation:
- [ ] `[panapana_master_debug]` shows all functions as EXISTS
- [ ] CSV upload form appears correctly
- [ ] Sample CSV processes without errors
- [ ] Test course creates successfully

### ✅ Permissions:
- [ ] Admin users can access all features
- [ ] Gestor users can access course creation
- [ ] Other users are properly restricted

### ✅ File Structure:
```
wp-content/themes/geeks-child/
├── functions.php
├── style.css
└── tutor/
    ├── course-automation-loader.php
    ├── core/
    │   ├── capabilities.php
    │   ├── csv-parser.php
    │   ├── course-creator.php
    │   ├── course-exporter.php
    │   └── quiz-creator.php
    └── shortcodes/
        └── course-shortcodes.php
```

## 🚨 Troubleshooting

### Common Issues:

#### Shortcodes Show as Plain Text:
- Check if child theme is active
- Verify `functions.php` loads the automation system
- Check PHP error logs for loading issues

#### "Function Not Found" Errors:
- Verify all files are in correct locations
- Check file permissions
- Review PHP error logs

#### CSV Upload Fails:
- Check PHP upload limits
- Verify file format matches specification
- Review CSV for syntax errors

#### Database Connection Issues:
- Verify WordPress database configuration
- Check MySQL service status
- Confirm database user permissions

### Debug Tools:
- Use `tests/debug-shortcodes.php` for system diagnostics
- Check WordPress debug logs
- Use `[panapana_master_debug]` shortcode for status

## 📞 Getting Help

1. **Check Documentation**: Review all docs/ files
2. **Debug First**: Use built-in debugging tools
3. **Check Logs**: Review PHP and WordPress error logs
4. **GitHub Issues**: Search existing issues or create new one

## 🔄 Updates and Maintenance

### Regular Maintenance:
- Keep WordPress core updated
- Update Tutor LMS plugin regularly
- Monitor PHP error logs
- Backup database before major changes

### Updating the System:
```bash
# Pull latest changes
git pull origin main

# Test in staging environment first
# Deploy to production after verification
```

---

**Need help? Check the troubleshooting guide or create a GitHub issue!**
