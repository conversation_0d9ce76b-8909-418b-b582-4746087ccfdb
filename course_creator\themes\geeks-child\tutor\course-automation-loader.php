<?php
/**
 * <PERSON><PERSON> LMS Course Automation - Main Loader
 *
 * This file loads all the modular components of the course automation system.
 * Include this file from functions.php to activate all functionality.
 *
 * @package TutorLMS_CourseAutomation
 * @version 2.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

// Define constants for the automation system
define( 'PANAPANA_AUTOMATION_VERSION', '2.0.0' );
define( 'PANAPANA_AUTOMATION_PATH', get_stylesheet_directory() . '/tutor/' );
define( 'PANAPANA_AUTOMATION_URL', get_stylesheet_directory_uri() . '/tutor/' );

/**
 * Main class for Tutor LMS Course Automation system.
 */
class Panapana_Course_Automation {

	/**
	 * Single instance of the class.
	 *
	 * @var Panapana_Course_Automation
	 */
	private static $instance = null;

	/**
	 * Get single instance of the class.
	 *
	 * @return Panapana_Course_Automation
	 */
	public static function get_instance() {
		if ( null === self::$instance ) {
			self::$instance = new self();
		}
		return self::$instance;
	}

	/**
	 * Constructor.
	 */
	private function __construct() {
		$this->load_dependencies();
		$this->init_hooks();
	}

	/**
	 * Load required files based on context and need.
	 */
	private function load_dependencies() {
		// Always load core capabilities (lightweight, needed for permission checks)
		$this->load_file( 'core/capabilities.php' );

		// Load admin interface on admin pages
		if ( is_admin() ) {
			$this->load_admin_dependencies();
		}

		// Always load shortcodes on frontend (they must be registered early)
		if ( ! is_admin() ) {
			// Load dependencies that shortcodes need
			$this->load_file( 'core/course-exporter.php' );
			$this->load_file( 'core/csv-parser.php' );
			$this->load_file( 'core/course-creator.php' );
			$this->load_file( 'core/quiz-creator.php' );

			// Load shortcodes last (they depend on the above functions)
			$this->load_file( 'shortcodes/course-shortcodes.php' );
		}

		// Load other files conditionally based on context
		$this->conditional_load_dependencies();
	}

	/**
	 * Load admin-specific dependencies.
	 */
	private function load_admin_dependencies() {
		// Always load core functionality in admin
		$this->load_file( 'core/course-exporter.php' );
		$this->load_file( 'core/csv-parser.php' );
		$this->load_file( 'core/course-creator.php' );
		$this->load_file( 'core/quiz-creator.php' );

		// Load admin interface
		$this->load_file( 'admin/admin-menu.php' );
		$this->load_file( 'admin/ajax-handlers.php' );
	}

	/**
	 * Load files conditionally based on current context.
	 */
	private function conditional_load_dependencies() {
		// Note: Shortcodes are now loaded via init hook to ensure proper timing
		// This method handles other conditional loading

		// Load export functionality if export is requested
		if ( isset( $_GET['export_tutor_course'] ) || $this->is_export_context() ) {
			$this->load_file( 'core/course-exporter.php' );
		}

		// Load creation functionality if creation is requested or shortcodes are present
		if ( $this->is_creation_context() ) {
			$this->load_file( 'core/csv-parser.php' );
			$this->load_file( 'core/course-creator.php' );
			$this->load_file( 'core/quiz-creator.php' );
		}
	}

	/**
	 * Load a single file with error handling.
	 *
	 * @param string $file Relative file path.
	 */
	private function load_file( $file ) {
		$file_path = PANAPANA_AUTOMATION_PATH . $file;

		if ( file_exists( $file_path ) ) {
			require_once $file_path;
		} else {
			error_log( "Panapana Course Automation: Missing file - {$file_path}" );
		}
	}

	/**
	 * Check if we're in an export context.
	 *
	 * @return bool True if export functionality is needed.
	 */
	private function is_export_context() {
		global $post;

		// Check for export-related actions
		if ( isset( $_GET['export_tutor_course'] ) ) {
			return true;
		}

		// Check if current page might contain export shortcodes
		if ( $post && has_shortcode( $post->post_content, 'tutor_course_exporter' ) ) {
			return true;
		}

		return false;
	}

	/**
	 * Check if we're in a course creation context.
	 *
	 * @return bool True if creation functionality is needed.
	 */
	private function is_creation_context() {
		global $post;

		// Check for CSV upload
		if ( isset( $_POST['panapana_csv_upload'] ) || isset( $_POST['create_hello_world'] ) ) {
			return true;
		}

		// Check if current page might contain creation shortcodes
		if ( $post ) {
			$creation_shortcodes = array( 'panapana_csv_course_creator', 'panapana_hello_world_course' );
			foreach ( $creation_shortcodes as $shortcode ) {
				if ( has_shortcode( $post->post_content, $shortcode ) ) {
					return true;
				}
			}
		}

		// Always load on admin pages (for testing and admin interface)
		if ( is_admin() ) {
			return true;
		}

		return false;
	}

	/**
	 * Check if we're on an admin page that might use shortcodes.
	 *
	 * @return bool True if admin page might need shortcodes.
	 */
	private function is_admin_page_with_shortcodes() {
		$screen = get_current_screen();

		if ( ! $screen ) {
			return false;
		}

		// Load on post edit pages (might contain shortcodes)
		if ( in_array( $screen->base, array( 'post', 'page' ) ) ) {
			return true;
		}

		// Load on pages that might preview shortcodes
		if ( strpos( $screen->id, 'course' ) !== false ) {
			return true;
		}

		return false;
	}

	/**
	 * Initialize WordPress hooks.
	 */
	private function init_hooks() {
		// Add admin notices for missing dependencies
		add_action( 'admin_notices', array( $this, 'check_dependencies' ) );

		// Add custom styles for shortcodes
		add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_styles' ) );

		// Add admin styles
		add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_styles' ) );
	}

	/**
	 * Check for required dependencies and show admin notices if missing.
	 */
	public function check_dependencies() {
		// Only show admin notices on admin pages
		if ( ! is_admin() ) {
			return;
		}

		// Check if Tutor LMS is active - use a more reliable method
		if ( ! class_exists( 'TUTOR' ) && ! function_exists( 'tutor' ) ) {
			echo '<div class="notice notice-error"><p>';
			echo '<strong>Panapana Course Automation:</strong> Tutor LMS plugin is required but not active.';
			echo '</p></div>';
		}

		// Check if required database tables exist
		global $wpdb;
		$required_tables = array(
			$wpdb->prefix . 'tutor_quiz_questions',
			$wpdb->prefix . 'tutor_quiz_question_answers',
		);

		foreach ( $required_tables as $table ) {
			$table_exists = $wpdb->get_var( "SHOW TABLES LIKE '{$table}'" ) === $table;
			if ( ! $table_exists ) {
				echo '<div class="notice notice-error"><p>';
				echo "<strong>Panapana Course Automation:</strong> Required database table '{$table}' is missing.";
				echo '</p></div>';
			}
		}
	}

	/**
	 * Enqueue frontend styles.
	 */
	public function enqueue_styles() {
		// Only enqueue on pages that might contain our shortcodes
		if ( is_singular() || is_page() ) {
			wp_add_inline_style( 'wp-block-library', $this->get_inline_css() );
		}
	}

	/**
	 * Enqueue admin styles.
	 */
	public function enqueue_admin_styles() {
		wp_add_inline_style( 'wp-admin', $this->get_admin_inline_css() );
	}

	/**
	 * Get inline CSS for shortcodes.
	 *
	 * @return string CSS styles.
	 */
	private function get_inline_css() {
		return '
			.panapana-csv-course-creator,
			.panapana-hello-world-creator,
			.panapana-debug-info,
			.tutor-course-exporter {
				max-width: 800px;
				margin: 20px 0;
				padding: 20px;
				border: 1px solid #ddd;
				border-radius: 8px;
				background-color: #f9f9f9;
			}
			
			.panapana-message {
				padding: 12px;
				margin: 15px 0;
				border-radius: 5px;
				font-weight: 500;
			}
			
			.panapana-message-success {
				background-color: #d4edda;
				border: 1px solid #c3e6cb;
				color: #155724;
			}
			
			.panapana-message-error {
				background-color: #f8d7da;
				border: 1px solid #f5c6cb;
				color: #721c24;
			}
			
			.panapana-csv-help {
				margin-top: 20px;
				padding: 15px;
				background-color: #e7f3ff;
				border-left: 4px solid #0073aa;
				border-radius: 0 5px 5px 0;
			}
			
			.panapana-csv-help h4 {
				margin-top: 0;
				color: #0073aa;
			}
			
			.tutor-course-exporter ul {
				list-style: none;
				padding: 0;
				border: 1px solid #ddd;
				border-radius: 5px;
				max-width: 600px;
			}
			
			.tutor-course-exporter li {
				padding: 12px;
				border-bottom: 1px solid #eee;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
			
			.tutor-course-exporter li:last-child {
				border-bottom: none;
			}
			
			.tutor-btn {
				display: inline-block;
				padding: 8px 16px;
				background-color: #0073aa;
				color: white;
				text-decoration: none;
				border-radius: 3px;
				font-size: 14px;
				transition: background-color 0.3s;
			}
			
			.tutor-btn:hover {
				background-color: #005a87;
				color: white;
			}
		';
	}

	/**
	 * Get inline CSS for admin pages.
	 *
	 * @return string Admin CSS styles.
	 */
	private function get_admin_inline_css() {
		return '
			.panapana-admin-notice {
				padding: 12px;
				margin: 15px 0;
				border-radius: 5px;
			}
		';
	}

	/**
	 * Get system information for debugging.
	 *
	 * @return array System information.
	 */
	public static function get_system_info() {
		global $wpdb;
		
		return array(
			'version' => PANAPANA_AUTOMATION_VERSION,
			'wordpress_version' => get_bloginfo( 'version' ),
			'tutor_active' => class_exists( 'TUTOR' ),
			'required_tables' => array(
				$wpdb->prefix . 'tutor_quiz_questions' => $wpdb->get_var( "SHOW TABLES LIKE '{$wpdb->prefix}tutor_quiz_questions'" ) === $wpdb->prefix . 'tutor_quiz_questions',
				$wpdb->prefix . 'tutor_quiz_question_answers' => $wpdb->get_var( "SHOW TABLES LIKE '{$wpdb->prefix}tutor_quiz_question_answers'" ) === $wpdb->prefix . 'tutor_quiz_question_answers',
			),
			'user_capabilities' => array(
				'export_course_data' => current_user_can( 'export_course_data' ),
				'manage_options' => current_user_can( 'manage_options' ),
			),
		);
	}
}

/**
 * Initialize the automation system.
 */
function panapana_init_course_automation() {
	return Panapana_Course_Automation::get_instance();
}

// Initialize the system
panapana_init_course_automation();

/**
 * Helper function to get system information.
 * Can be used for debugging and support.
 *
 * @return array System information.
 */
function panapana_get_automation_info() {
	return Panapana_Course_Automation::get_system_info();
}
